const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接池
const pool = new Pool({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: false,
    max: 1,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
});

async function checkDatabase() {
    const client = await pool.connect();
    try {
        console.log('🔍 正在查询数据库...\n');

        // 查询交易记录总数
        const transactionCount = await client.query('SELECT COUNT(*) FROM transactions');
        console.log('📊 交易记录总数:', transactionCount.rows[0].count);

        // 查询会员总数
        const memberCount = await client.query('SELECT COUNT(*) FROM members');
        console.log('👥 会员总数:', memberCount.rows[0].count);

        // 查询用户总数
        const userCount = await client.query('SELECT COUNT(*) FROM users');
        console.log('👤 用户总数:', userCount.rows[0].count);

        // 查询代金券总数
        const voucherCount = await client.query('SELECT COUNT(*) FROM vouchers');
        console.log('🎫 代金券总数:', voucherCount.rows[0].count);

        // 查询交易记录类型分布
        const transactionTypes = await client.query(`
            SELECT type, COUNT(*) as count 
            FROM transactions 
            GROUP BY type 
            ORDER BY count DESC
        `);
        
        if (transactionTypes.rows.length > 0) {
            console.log('\n📈 交易记录类型分布:');
            transactionTypes.rows.forEach(row => {
                console.log(`  ${row.type}: ${row.count} 条`);
            });
        }

        // 查询最近5条交易记录
        const recentTransactions = await client.query(`
            SELECT id, type, member_name, member_phone, points, 
                   TO_CHAR(created_at AT TIME ZONE 'Asia/Shanghai', 'YYYY-MM-DD HH24:MI:SS') as created_time
            FROM transactions 
            ORDER BY created_at DESC 
            LIMIT 5
        `);

        if (recentTransactions.rows.length > 0) {
            console.log('\n🕒 最近5条交易记录:');
            recentTransactions.rows.forEach((row, index) => {
                console.log(`  ${index + 1}. [${row.type}] ${row.member_name} (${row.member_phone}) - ${row.points}积分 - ${row.created_time}`);
            });
        } else {
            console.log('\n🕒 暂无交易记录');
        }

        // 查询积分统计
        const pointsStats = await client.query(`
            SELECT 
                SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as total_earned,
                SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as total_redeemed,
                SUM(points) as net_points
            FROM transactions
        `);

        if (pointsStats.rows[0].total_earned) {
            console.log('\n💰 积分统计:');
            console.log(`  总获得积分: ${pointsStats.rows[0].total_earned || 0}`);
            console.log(`  总兑换积分: ${pointsStats.rows[0].total_redeemed || 0}`);
            console.log(`  净积分: ${pointsStats.rows[0].net_points || 0}`);
        }

    } catch (error) {
        console.error('❌ 查询数据库失败:', error.message);
    } finally {
        client.release();
        await pool.end();
    }
}

checkDatabase();
