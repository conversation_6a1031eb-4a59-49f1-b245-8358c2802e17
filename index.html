<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>欧妮家服装工作室会员积分管理系统</title>
    <!-- SheetJS库用于Excel导出 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            height: 100%;
            width: 100%;
            overflow-x: hidden;
        }

        /* 桌面端样式 */
        @media (min-width: 768px) {
            body {
                padding: 10px;
            }
        }

        /* 移动端样式 */
        @media (max-width: 767px) {
            html, body {
                margin: 0 !important;
                padding: 0 !important;
                height: 100% !important;
                width: 100% !important;
                overflow-x: hidden;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                /* 处理安全区域 */
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
                padding-left: env(safe-area-inset-left);
                padding-right: env(safe-area-inset-right);
            }

            /* 移动端导航栏文字换行 */
            .nav-tab {
                line-height: 1.2 !important;
                white-space: pre-line !important;
                padding: 8px 5px !important;
                font-size: 14px !important;
            }
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            overflow: hidden;
        }

        /* 桌面端容器样式 */
        @media (min-width: 768px) {
            .container {
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            }
        }

        /* 移动端容器样式 - 完全全屏 */
        @media (max-width: 767px) {
            .container {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                width: 100% !important;
                height: 100% !important;
                max-width: none !important;
                margin: 0 !important;
                padding: 0 !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                overflow-y: auto;
            }

            /* 移动端头部调整 */
            .header {
                padding: 15px 20px !important;
            }

            /* 移动端选项卡调整 */
            .tabs {
                padding: 0 10px !important;
            }

            .tab {
                padding: 12px 8px !important;
                font-size: 14px !important;
            }

            /* 移动端内容区域调整 */
            .tab-content {
                padding: 15px !important;
            }

            /* 移动端输入框调整 */
            input, select, button {
                font-size: 16px !important; /* 防止iOS缩放 */
            }
        }

        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 20px;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }

        .tab-content {
            display: none;
            padding: 20px;
            animation: fadeIn 0.3s ease;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .member-info {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .member-info h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }

        .points-display {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }

        .voucher-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .voucher-card {
            background: linear-gradient(135deg, #a8b5ff, #c8d6ff);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .voucher-card:hover {
            transform: scale(1.02);
        }

        .voucher-card h4 {
            color: #2d3436;
            margin-bottom: 5px;
        }

        .voucher-card p {
            color: #636e72;
            font-size: 12px;
        }

        .history-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .history-item h4 {
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .history-item p {
            color: #666;
            font-size: 12px;
            margin-bottom: 3px;
        }

        .transaction-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            justify-content: flex-end;
        }

        .transaction-edit-btn {
            background: #ffa500;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .transaction-edit-btn:hover {
            background: #ff8c00;
        }

        .transaction-delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .transaction-delete-btn:hover {
            background: #c82333;
        }

        /* 交易记录搜索框样式 */
        .transaction-search-container {
            position: sticky;
            top: 0;
            background: white;
            padding: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 15px;
            z-index: 100;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .transaction-search-box {
            position: relative;
            max-width: 400px;
            margin: 0 auto;
        }

        .transaction-search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .transaction-search-input:focus {
            border-color: #667eea;
        }

        .transaction-search-input::placeholder {
            color: #999;
        }

        .transaction-search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-size: 16px;
        }

        .transaction-search-clear {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            background: #ccc;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: none;
        }

        .transaction-search-clear:hover {
            background: #999;
        }

        .transaction-search-results {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .member-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .member-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .member-details {
            flex: 1;
        }

        .member-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .member-phone {
            color: #666;
            font-size: 12px;
        }

        .member-points {
            text-align: right;
            color: #667eea;
            font-weight: bold;
        }

        .member-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .member-edit-btn {
            background: #ffa500;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .member-edit-btn:hover {
            background: #ff8c00;
        }

        .member-delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s ease;
        }

        .member-delete-btn:hover {
            background: #c82333;
        }

        .voucher-settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .voucher-setting-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e1e5e9;
        }

        .voucher-setting-item label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
        }

        .voucher-setting-item input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .voucher-setting-item .unit {
            display: block;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }

        .custom-voucher-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid #e1e5e9;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .custom-voucher-info {
            flex: 1;
        }

        .custom-voucher-info strong {
            color: #333;
            font-size: 16px;
        }

        .custom-voucher-info span {
            color: #666;
            font-size: 14px;
        }

        .voucher-actions {
            display: flex;
            gap: 8px;
        }

        .custom-voucher-edit {
            background: #ffa500;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .custom-voucher-remove {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .edit-voucher-form {
            display: flex;
            flex-wrap: nowrap;
            gap: 6px;
            align-items: center;
            flex: 1;
        }

        .edit-voucher-form span {
            font-size: 12px;
            white-space: nowrap;
            color: #666;
        }

        .edit-voucher-form input {
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            width: 50px;
            min-width: 45px;
            text-align: center;
        }

        .edit-voucher-form button {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            white-space: nowrap;
        }

        .save-btn {
            background: #28a745;
            color: white;
        }

        .cancel-btn {
            background: #6c757d;
            color: white;
        }

        /* 登录页面样式 */
        .login-container {
            max-width: 350px;
            margin: 50px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            animation: slideIn 0.5s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .login-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }

        .login-header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .login-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .login-form {
            padding: 30px 25px;
        }

        .login-form .form-group {
            margin-bottom: 25px;
        }

        .login-form input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .login-form input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 12px;
        }

        .user-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            margin-bottom: 10px;
        }

        .user-info span {
            color: white;
            font-size: 14px;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 5px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .hidden {
            display: none !important;
        }

        /* 用户管理样式 */
        .user-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #e1e5e9;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            font-size: 16px;
        }

        .user-info {
            color: #666;
            font-size: 12px;
            margin-bottom: 3px;
        }

        .user-role {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .role-admin {
            background: #667eea;
            color: white;
        }

        .role-staff {
            background: #28a745;
            color: white;
        }

        .role-owner {
            background: #764ba2;
            color: white;
        }

        .user-actions {
            display: flex;
            gap: 8px;
        }

        .user-edit-btn {
            background: #ffa500;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .user-delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
        }

        .user-edit-btn:hover {
            background: #ff8c00;
        }

        .user-delete-btn:hover {
            background: #c82333;
        }

        /* 设置页面选项卡样式 */
        .settings-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            border: 1px solid #e1e5e9;
        }

        .settings-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
            position: relative;
        }

        .settings-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .settings-tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .settings-tab-content {
            display: none;
            animation: fadeIn 0.3s ease;
        }

        .settings-tab-content.active {
            display: block;
        }

        /* 用户管理在设置页面中的样式优化 */
        #user-management-content .stats-grid {
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        #user-management-content .stat-card {
            padding: 15px;
        }

        #user-management-content .stat-number {
            font-size: 20px;
        }

        @media (max-width: 480px) {
            .settings-tab {
                padding: 12px 15px;
                font-size: 13px;
            }

            #user-management-content .stats-grid {
                grid-template-columns: 1fr;
            }

            #user-management-content .form-group {
                margin-bottom: 15px;
            }

            #user-management-content div[style*="grid-template-columns"] {
                grid-template-columns: 1fr !important;
            }
        }

        /* 响应式设计 - 小屏幕优化 */
        @media (max-width: 480px) {
            .container {
                margin: 5px;
                border-radius: 15px;
            }

            .login-container {
                margin: 20px auto;
                max-width: calc(100% - 10px);
                border-radius: 15px;
            }

            .header {
                padding: 15px;
            }

            .header h1 {
                font-size: 16px;
            }

            .nav-tab {
                padding: 12px 8px;
                font-size: 12px;
            }

            .tab-content {
                padding: 15px;
            }

            /* 代金券添加表单在小屏幕上垂直排列 */
            .voucher-add-form {
                flex-direction: column !important;
                align-items: stretch !important;
            }

            .voucher-add-form input,
            .voucher-add-form button {
                width: 100% !important;
                min-width: auto !important;
                margin-bottom: 8px;
            }

            .voucher-add-form button {
                margin-bottom: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="login-page" class="login-container">
        <div class="login-header">
            <h1>欧妮家服装工作室</h1>
            <p>会员积分管理系统</p>
        </div>

        <div class="login-form">
            <div class="form-group">
                <input type="text" id="username" placeholder="请输入用户名" autocomplete="username">
            </div>

            <div class="form-group">
                <input type="password" id="password" placeholder="请输入密码" autocomplete="current-password">
            </div>

            <button class="login-btn" onclick="login()">登录</button>

            <div id="login-message"></div>
        </div>

        <div class="login-footer">
            <p>© 2024 欧妮家服装工作室 版权所有</p>
        </div>
    </div>

    <!-- 主应用页面 -->
    <div id="main-app" class="container hidden">
        <div class="header">
            <div class="user-info">
                <span id="current-user">欢迎，管理员</span>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
            <h1>欧妮家服装工作室</h1>
            <p>会员积分管理系统</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('sales')">销售
积分</button>
            <button class="nav-tab" onclick="showTab('redeem')">积分
兑换</button>
            <button class="nav-tab" onclick="showTab('member')">会员
管理</button>
            <button class="nav-tab" onclick="showTab('history')">交易
记录</button>
            <button class="nav-tab" onclick="showTab('settings')">设置</button>
        </div>

        <!-- 销售积分页面 -->
        <div id="sales-tab" class="tab-content active">
            <div class="form-group">
                <label>会员手机号</label>
                <input type="text" id="sales-phone" placeholder="请输入会员手机号">
            </div>
            
            <div class="form-group">
                <label>销售金额 (元)</label>
                <input type="number" id="sales-amount" placeholder="请输入销售金额" step="0.01">
            </div>
            
            <div class="form-group">
                <label>积分比例</label>
                <select id="points-ratio">
                    <option value="default">默认会员比例</option>
                    <option value="vip">VIP会员比例</option>
                </select>
            </div>
            
            <button class="btn btn-primary" onclick="addPoints()">添加积分</button>
            
            <div id="sales-message"></div>
        </div>

        <!-- 积分兑换页面 -->
        <div id="redeem-tab" class="tab-content">
            <div class="form-group">
                <label>会员手机号</label>
                <input type="text" id="redeem-phone" placeholder="请输入会员手机号">
            </div>
            
            <button class="btn btn-success" onclick="queryMember()">查询会员信息</button>
            
            <div id="member-display"></div>
            
            <div class="voucher-grid" id="voucher-grid">
                <!-- 代金券将通过JavaScript动态生成 -->
            </div>
            
            <div id="redeem-message"></div>
        </div>

        <!-- 会员管理页面 -->
        <div id="member-tab" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-members">0</div>
                    <div class="stat-label">总会员数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="total-points">0</div>
                    <div class="stat-label">总积分数</div>
                </div>
            </div>
            
            <div class="form-group">
                <label>会员姓名</label>
                <input type="text" id="member-name" placeholder="请输入会员姓名">
            </div>
            
            <div class="form-group">
                <label>手机号</label>
                <input type="text" id="member-phone" placeholder="请输入手机号">
            </div>
            
            <button class="btn btn-primary" onclick="addMember()">添加会员</button>
            
            <div id="member-message"></div>
            
            <div class="member-list" id="member-list"></div>
        </div>

        <!-- 交易记录页面 -->
        <div id="history-tab" class="tab-content">
            <div id="history-message"></div>

            <!-- 搜索框 -->
            <div class="transaction-search-container">
                <div class="transaction-search-box">
                    <input type="text"
                           id="transaction-search-input"
                           class="transaction-search-input"
                           placeholder="搜索手机号或会员姓名..."
                           oninput="searchTransactions()"
                           onkeyup="handleSearchKeyup(event)">
                    <button class="transaction-search-clear"
                            id="transaction-search-clear"
                            onclick="clearTransactionSearch()"
                            title="清除搜索">×</button>
                    <span class="transaction-search-icon">🔍</span>
                </div>
                <div class="transaction-search-results" id="transaction-search-results"></div>
            </div>

            <div id="transaction-history"></div>
        </div>



        <!-- 设置页面 -->
        <div id="settings-tab" class="tab-content">
            <!-- 设置页面选项卡 -->
            <div class="settings-tabs">
                <button class="settings-tab active" onclick="showSettingsTab('system')">系统设置</button>
                <button class="settings-tab" onclick="showSettingsTab('users')">用户管理</button>
            </div>
            
            <!-- 系统设置选项卡内容 -->
            <div id="system-settings-content" class="settings-tab-content active">
                <!-- 积分比例设置 -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                        <span style="background: #667eea; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 12px;">积</span>
                        积分获得比例设置
                    </h4>
                
                <div class="form-group">
                    <label>默认积分比例 (1元可获得积分数)</label>
                    <input type="number" id="default-points-ratio" value="1" min="0.1" step="0.1" placeholder="例如：1元=1积分，输入1">
                </div>
                
                <div class="form-group">
                    <label>VIP会员积分比例 (1元可获得积分数)</label>
                    <input type="number" id="vip-points-ratio" value="2" min="0.1" step="0.1" placeholder="例如：1元=2积分，输入2">
                </div>
                
                <button class="btn btn-primary" onclick="savePointsRatio()">保存积分比例</button>
            </div>
            
            <!-- 代金券设置 -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                    <span style="background: #764ba2; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 12px;">券</span>
                    代金券管理
                </h4>
                
                <!-- 添加新代金券 -->
                <div style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 15px; border: 1px solid #e1e5e9;">
                    <h5 style="color: #333; margin-bottom: 15px;">添加新代金券</h5>
                    <div class="voucher-add-form" style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 10px;">
                        <input type="number" id="custom-voucher-amount" placeholder="代金券金额" style="flex: 1; min-width: 120px; padding: 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                        <input type="number" id="custom-voucher-points" placeholder="所需积分" style="flex: 1; min-width: 120px; padding: 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;">
                        <button style="padding: 10px 15px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; min-width: 60px;" onclick="addCustomVoucher()">添加</button>
                    </div>
                </div>
                
                <!-- 现有代金券列表 -->
                <div>
                    <h5 style="color: #333; margin-bottom: 15px;">现有代金券</h5>
                    <div id="all-vouchers-list"></div>
                </div>
            </div>
            
            <!-- 数据管理 -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <h4 style="color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                    <span style="background: #667eea; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-size: 12px;">🗂</span>
                    数据管理
                </h4>

                <button class="btn" style="background: #667eea; color: white; margin-bottom: 10px;" onclick="exportData()">导出数据</button>
                <button class="btn" style="background: #764ba2; color: white;" onclick="clearAllData()">清空所有数据</button>
            </div>
            </div>

            <!-- 用户管理选项卡内容 -->
            <div id="user-management-content" class="settings-tab-content">
                <!-- 用户统计 -->
                <div class="stats-grid" style="margin-bottom: 20px;">
                    <div class="stat-card">
                        <div class="stat-number" id="total-users">0</div>
                        <div class="stat-label">总用户数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="online-users">1</div>
                        <div class="stat-label">在线用户</div>
                    </div>
                </div>

                <!-- 添加新用户 -->
                <div style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 15px; border: 1px solid #e1e5e9;">
                    <h5 style="color: #333; margin-bottom: 15px;">添加新用户</h5>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div class="form-group">
                            <label>用户名</label>
                            <input type="text" id="new-username" placeholder="请输入用户名" style="padding: 8px 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; width: 100%;">
                        </div>

                        <div class="form-group">
                            <label>姓名</label>
                            <input type="text" id="new-name" placeholder="请输入姓名" style="padding: 8px 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; width: 100%;">
                        </div>

                        <div class="form-group">
                            <label>密码</label>
                            <input type="password" id="new-password" placeholder="请输入密码" style="padding: 8px 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; width: 100%;">
                        </div>

                        <div class="form-group">
                            <label>角色</label>
                            <select id="new-role" style="padding: 8px 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; width: 100%;">
                                <option value="staff">店员</option>
                                <option value="admin">管理员</option>
                                <option value="owner">店主</option>
                            </select>
                        </div>
                    </div>

                    <button class="btn btn-primary" onclick="addUser()">添加用户</button>
                </div>

                <!-- 用户列表 -->
                <div style="background: white; padding: 15px; border-radius: 10px; border: 1px solid #e1e5e9;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h5 style="color: #333; margin: 0;">用户列表</h5>
                        <input type="text" id="user-search" placeholder="搜索用户..."
                               style="padding: 6px 10px; border: 1px solid #ddd; border-radius: 6px; font-size: 12px; width: 150px;"
                               onkeyup="searchUsers()">
                    </div>
                    <div id="users-list"></div>
                </div>
            </div>

            <div id="settings-message"></div>
        </div>
    </div>

    <script>
        // API配置
        const API_BASE_URL = window.location.origin;
        let authToken = localStorage.getItem('authToken');

        // 用户认证相关
        let currentUser = null;
        let isLoggedIn = false;

        // 数据存储
        let members = {};
        let transactions = [];

        // 系统设置
        let systemSettings = {
            defaultPointsRatio: 1,
            vipPointsRatio: 2,
            customVouchers: []
        };

        // API请求封装
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE_URL}/api${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };

            if (authToken) {
                config.headers['Authorization'] = `Bearer ${authToken}`;
            }



            try {
                const response = await fetch(url, config);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '请求失败');
                }

                return data;
            } catch (error) {
                console.error('❌ API请求错误:', error);
                throw error;
            }
        }

        // 登录功能
        async function login() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                showLoginMessage('请输入用户名和密码', 'error');
                return;
            }

            try {
                const response = await apiRequest('/login', {
                    method: 'POST',
                    body: JSON.stringify({ username, password })
                });

                if (response.success) {
                    authToken = response.token;
                    currentUser = response.user;
                    isLoggedIn = true;

                    // 保存令牌到localStorage
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));

                    // 更新UI
                    document.getElementById('current-user').textContent = `欢迎，${currentUser.name}`;
                    document.getElementById('login-page').classList.add('hidden');
                    document.getElementById('main-app').classList.remove('hidden');

                    // 初始化应用数据
                    await initializeApp();

                    showLoginMessage('登录成功', 'success');
                }
            } catch (error) {
                showLoginMessage(error.message || '登录失败', 'error');
            }
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                currentUser = null;
                isLoggedIn = false;
                authToken = null;

                // 清除本地存储
                localStorage.removeItem('authToken');
                localStorage.removeItem('currentUser');

                // 清空登录表单
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';

                // 更新UI
                document.getElementById('login-page').classList.remove('hidden');
                document.getElementById('main-app').classList.add('hidden');

                showLoginMessage('已退出登录', 'success');
            }
        }

        // 显示登录消息
        function showLoginMessage(message, type) {
            const messageElement = document.getElementById('login-message');
            messageElement.innerHTML = `
                <div class="alert alert-${type === 'error' ? 'error' : 'success'}">
                    ${message}
                </div>
            `;

            // 3秒后自动清除消息
            setTimeout(() => {
                messageElement.innerHTML = '';
            }, 3000);
        }

        // 初始化应用
        async function initializeApp() {
            try {
                await loadSystemSettings();
                await loadMembers();
                await loadTransactions();
                await loadVouchers();
                updateMemberStats();
                displayMembers();
                updateVoucherDisplay();
                updatePointsRatioOptions();

                // 根据权限显示或隐藏用户管理选项卡
                updateUserManagementTabVisibility();
            } catch (error) {
                console.error('初始化应用失败:', error);
                showMessage('sales-message', '加载数据失败，请刷新页面重试', 'error');
            }
        }

        // 更新用户管理选项卡的可见性
        function updateUserManagementTabVisibility() {
            const userTab = document.querySelector('.settings-tab[onclick*="users"]');
            if (currentUser && (currentUser.role === 'admin' || currentUser.role === 'owner')) {
                if (userTab) userTab.style.display = 'block';
            } else {
                if (userTab) userTab.style.display = 'none';
            }
        }

        // 加载系统设置
        async function loadSystemSettings() {
            try {
                const response = await apiRequest('/settings');
                if (response.success) {
                    systemSettings.defaultPointsRatio = parseFloat(response.settings.default_points_ratio || 1);
                    systemSettings.vipPointsRatio = parseFloat(response.settings.vip_points_ratio || 2);
                }
            } catch (error) {
                console.error('加载系统设置失败:', error);
            }
        }

        // 加载会员数据
        async function loadMembers() {
            try {
                const response = await apiRequest('/members');
                if (response.success) {
                    members = {};
                    response.members.forEach(member => {
                        members[member.phone] = {
                            name: member.name,
                            points: member.points,
                            joinDate: member.join_date
                        };
                    });
                }
            } catch (error) {
                console.error('加载会员数据失败:', error);
            }
        }

        // 交易记录分页变量
        let transactionPagination = {
            currentPage: 1,
            totalPages: 1,
            totalRecords: 0,
            limit: 20,
            hasMore: true,
            loading: false
        };

        // 加载交易记录（支持分页和搜索）
        async function loadTransactions(page = 1, search = '', append = false) {
            try {
                // 防止重复加载
                if (transactionPagination.loading) return;

                transactionPagination.loading = true;

                // 显示加载状态
                if (page === 1) {
                    showTransactionLoading();
                } else {
                    showLoadMoreIndicator();
                }

                const params = new URLSearchParams({
                    page: page,
                    limit: transactionPagination.limit,
                    search: search
                });

                const response = await apiRequest(`/transactions?${params}`);

                if (response.success) {
                    const newTransactions = response.transactions.map(t => ({
                        id: t.id,
                        type: t.type,
                        phone: t.member_phone,
                        name: t.member_name,
                        amount: t.amount,
                        points: t.points,
                        voucherAmount: t.voucher_amount,
                        ratio: t.ratio,
                        timestamp: t.formatted_time || formatDateTime(t.created_at)
                    }));

                    // 更新分页信息
                    transactionPagination = {
                        ...transactionPagination,
                        currentPage: response.pagination.currentPage,
                        totalPages: response.pagination.totalPages,
                        totalRecords: response.pagination.totalRecords,
                        hasMore: response.pagination.hasMore,
                        loading: false
                    };

                    if (append) {
                        // 追加到现有数据
                        transactions = [...transactions, ...newTransactions];
                        filteredTransactions = [...filteredTransactions, ...newTransactions];
                    } else {
                        // 替换数据（新搜索或刷新）
                        transactions = newTransactions;
                        filteredTransactions = newTransactions;
                    }

                    hideTransactionLoading();
                    return true;
                }
            } catch (error) {
                console.error('加载交易记录失败:', error);
                transactionPagination.loading = false;
                hideTransactionLoading();
                return false;
            }
        }

        // 加载更多交易记录
        async function loadMoreTransactions() {
            if (!transactionPagination.hasMore || transactionPagination.loading) {
                return;
            }

            const nextPage = transactionPagination.currentPage + 1;
            const success = await loadTransactions(nextPage, currentSearchTerm, true);

            if (success) {
                displayTransactionHistory();
            }
        }

        // 加载代金券
        async function loadVouchers() {
            try {
                const response = await apiRequest('/vouchers');
                if (response.success) {
                    systemSettings.customVouchers = response.vouchers.map(v => ({
                        id: v.id,
                        amount: v.amount,
                        points: v.points
                    }));
                }
            } catch (error) {
                console.error('加载代金券失败:', error);
            }
        }

        // 存储所有用户数据
        let allUsers = [];

        // 加载用户数据
        async function loadUsers() {
            try {
                const response = await apiRequest('/users');

                if (response.success) {
                    allUsers = response.users;
                    displayUsers(allUsers);
                    updateUserStats(allUsers);
                } else {
                    showMessage('settings-message', response.error || '加载用户数据失败', 'error');
                }
            } catch (error) {
                console.error('❌ 加载用户数据异常:', error);
                if (error.message.includes('权限不足')) {
                    showMessage('settings-message', '权限不足，无法加载用户数据', 'error');
                } else {
                    showMessage('settings-message', '网络错误，无法加载用户数据', 'error');
                }
            }
        }

        // 搜索用户
        function searchUsers() {
            const searchTerm = document.getElementById('user-search').value.toLowerCase().trim();

            if (!searchTerm) {
                displayUsers(allUsers);
                return;
            }

            const filteredUsers = allUsers.filter(user =>
                user.name.toLowerCase().includes(searchTerm) ||
                user.username.toLowerCase().includes(searchTerm) ||
                user.role.toLowerCase().includes(searchTerm)
            );

            displayUsers(filteredUsers);
        }

        // 显示用户列表
        function displayUsers(users) {
            const usersList = document.getElementById('users-list');
            usersList.innerHTML = '';

            if (users.length === 0) {
                usersList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无用户</p>';
                return;
            }

            users.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';

                const roleClass = `role-${user.role}`;
                const roleText = {
                    'admin': '管理员',
                    'staff': '店员',
                    'owner': '店主'
                }[user.role] || user.role;

                // 检查是否是当前用户
                const isCurrentUser = currentUser && currentUser.id === user.id;
                const deleteButtonDisabled = isCurrentUser ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : '';
                const deleteButtonText = isCurrentUser ? '当前用户' : '删除';

                userItem.innerHTML = `
                    <div class="user-details">
                        <div class="user-name">
                            ${user.name}
                            ${isCurrentUser ? '<span style="color: #667eea; font-size: 12px; margin-left: 8px;">(当前用户)</span>' : ''}
                        </div>
                        <div class="user-info">用户名: ${user.username}</div>
                        <div class="user-info">创建时间: ${new Date(user.created_at).toLocaleDateString()}</div>
                        <span class="user-role ${roleClass}">${roleText}</span>
                    </div>
                    <div class="user-actions">
                        <button class="user-edit-btn" onclick="editUser(${user.id}, '${user.name}', '${user.role}', '${user.username}')">编辑</button>
                        <button class="user-delete-btn" ${deleteButtonDisabled} onclick="${isCurrentUser ? '' : `deleteUser(${user.id}, '${user.username}')`}">${deleteButtonText}</button>
                    </div>
                `;
                usersList.appendChild(userItem);
            });
        }

        // 更新用户统计
        function updateUserStats(users) {
            document.getElementById('total-users').textContent = users.length;
            // 在线用户数暂时显示为1（当前用户）
            document.getElementById('online-users').textContent = '1';
        }

        // 添加用户
        async function addUser() {
            const username = document.getElementById('new-username').value.trim();
            const name = document.getElementById('new-name').value.trim();
            const password = document.getElementById('new-password').value.trim();
            const role = document.getElementById('new-role').value;

            // 表单验证
            if (!username || !name || !password || !role) {
                showMessage('settings-message', '请填写完整信息', 'error');
                return;
            }

            if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
                showMessage('settings-message', '用户名只能包含字母、数字和下划线，长度3-20位', 'error');
                return;
            }

            if (password.length < 6) {
                showMessage('settings-message', '密码长度至少6位', 'error');
                return;
            }

            if (name.length < 2) {
                showMessage('settings-message', '姓名至少2个字符', 'error');
                return;
            }

            try {
                // 禁用按钮防止重复提交
                const addButton = event.target;
                addButton.disabled = true;
                addButton.textContent = '添加中...';

                const response = await apiRequest('/users', {
                    method: 'POST',
                    body: JSON.stringify({ username, name, password, role })
                });

                if (response.success) {
                    showMessage('settings-message', `成功添加用户：${name}`, 'success');

                    // 清空表单
                    document.getElementById('new-username').value = '';
                    document.getElementById('new-name').value = '';
                    document.getElementById('new-password').value = '';
                    document.getElementById('new-role').value = 'staff';

                    // 重新加载用户列表
                    await loadUsers();
                }
            } catch (error) {
                showMessage('settings-message', error.message || '添加用户失败', 'error');
            } finally {
                // 恢复按钮状态
                const addButton = document.querySelector('#user-management-content .btn-primary');
                if (addButton) {
                    addButton.disabled = false;
                    addButton.textContent = '添加用户';
                }
            }
        }

        // 编辑用户
        function editUser(userId, userName, userRole, username) {
            // 创建编辑表单模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 400px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">编辑用户</h3>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">用户名</label>
                        <input type="text" id="edit-username" value="${username}" disabled
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; background: #f5f5f5; color: #999;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">姓名</label>
                        <input type="text" id="edit-name" value="${userName}"
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">角色</label>
                        <select id="edit-role" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="staff" ${userRole === 'staff' ? 'selected' : ''}>店员</option>
                            <option value="admin" ${userRole === 'admin' ? 'selected' : ''}>管理员</option>
                            <option value="owner" ${userRole === 'owner' ? 'selected' : ''}>店主</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">新密码 (留空则不修改)</label>
                        <input type="password" id="edit-password" placeholder="请输入新密码"
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="closeEditModal()"
                                style="padding: 10px 20px; border: 1px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer;">
                            取消
                        </button>
                        <button onclick="saveUserEdit(${userId})"
                                style="padding: 10px 20px; border: none; background: #667eea; color: white; border-radius: 6px; cursor: pointer;">
                            保存
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentEditModal = modal;

            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeEditModal();
                }
            });
        }

        // 关闭编辑模态框
        function closeEditModal() {
            if (window.currentEditModal) {
                document.body.removeChild(window.currentEditModal);
                window.currentEditModal = null;
            }
        }

        // 保存用户编辑
        function saveUserEdit(userId) {
            const name = document.getElementById('edit-name').value.trim();
            const role = document.getElementById('edit-role').value;
            const password = document.getElementById('edit-password').value.trim();

            if (!name) {
                alert('姓名不能为空');
                return;
            }

            updateUser(userId, name, role, password);
        }

        // 更新用户
        async function updateUser(userId, name, role, password) {
            if (!name || !role) {
                alert('姓名和角色不能为空');
                return;
            }

            if (!['admin', 'staff', 'owner'].includes(role)) {
                alert('角色必须是管理员、店员或店主');
                return;
            }

            try {
                const body = { name, role };
                if (password) {
                    if (password.length < 6) {
                        alert('密码长度至少6位');
                        return;
                    }
                    body.password = password;
                }

                const response = await apiRequest(`/users/${userId}`, {
                    method: 'PUT',
                    body: JSON.stringify(body)
                });

                if (response.success) {
                    closeEditModal();
                    showMessage('settings-message', '用户信息更新成功', 'success');
                    await loadUsers();

                    // 如果修改的是当前用户的信息，更新当前用户信息
                    if (currentUser && currentUser.id === parseInt(userId)) {
                        currentUser.name = name;
                        currentUser.role = role;
                        document.getElementById('current-user').textContent = `欢迎，${name}`;
                        localStorage.setItem('currentUser', JSON.stringify(currentUser));
                    }
                }
            } catch (error) {
                alert(error.message || '更新用户失败');
            }
        }

        // 删除用户
        async function deleteUser(userId, username) {
            // 创建确认删除模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 400px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center;">
                    <div style="color: #dc3545; font-size: 48px; margin-bottom: 20px;">⚠️</div>
                    <h3 style="margin-bottom: 15px; color: #333;">确认删除用户</h3>
                    <p style="margin-bottom: 20px; color: #666; line-height: 1.5;">
                        您确定要删除用户 <strong>"${username}"</strong> 吗？<br>
                        <span style="color: #dc3545; font-weight: 500;">此操作不可恢复！</span>
                    </p>

                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="closeDeleteModal()"
                                style="padding: 10px 20px; border: 1px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer;">
                            取消
                        </button>
                        <button onclick="confirmDeleteUser(${userId})"
                                style="padding: 10px 20px; border: none; background: #dc3545; color: white; border-radius: 6px; cursor: pointer;">
                            确认删除
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentDeleteModal = modal;

            // 点击背景关闭模态框
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeDeleteModal();
                }
            });
        }

        // 关闭删除确认模态框
        function closeDeleteModal() {
            if (window.currentDeleteModal) {
                document.body.removeChild(window.currentDeleteModal);
                window.currentDeleteModal = null;
            }
        }

        // 确认删除用户
        async function confirmDeleteUser(userId) {
            try {
                const response = await apiRequest(`/users/${userId}`, {
                    method: 'DELETE'
                });

                if (response.success) {
                    closeDeleteModal();
                    showMessage('settings-message', response.message, 'success');
                    await loadUsers();
                }
            } catch (error) {
                closeDeleteModal();
                alert(error.message || '删除用户失败');
            }
        }

        // 键盘事件处理
        document.addEventListener('DOMContentLoaded', function() {
            // 登录表单回车键处理
            document.getElementById('username').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('password').focus();
                }
            });

            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });

        // 页面切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有导航标签的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');

            // 刷新特定页面的数据
            if (tabName === 'member') {
                updateMemberStats();
                displayMembers();
            } else if (tabName === 'history') {
                // 重置交易记录状态
                transactionPagination.currentPage = 1;
                transactionPagination.hasMore = true;
                currentSearchTerm = '';

                // 清除搜索状态
                const searchInput = document.getElementById('transaction-search-input');
                if (searchInput) {
                    searchInput.value = '';
                    document.getElementById('transaction-search-clear').style.display = 'none';
                    document.getElementById('transaction-search-results').textContent = '';
                }

                // 加载第一页数据
                loadTransactions(1, '', false).then(() => {
                    displayTransactionHistory();
                });
            } else if (tabName === 'redeem') {
                updateVoucherDisplay();
            } else if (tabName === 'settings') {
                loadSettingsValues();
                // 默认显示系统设置选项卡
                showSettingsTab('system');
            } else if (tabName === 'sales') {
                updatePointsRatioOptions();
            }
        }

        // 设置页面选项卡切换
        function showSettingsTab(tabName) {
            // 隐藏所有设置选项卡内容
            document.querySelectorAll('.settings-tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有设置选项卡的激活状态
            document.querySelectorAll('.settings-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的设置选项卡
            if (tabName === 'system') {
                document.getElementById('system-settings-content').classList.add('active');
                document.querySelector('.settings-tab[onclick*="system"]').classList.add('active');
            } else if (tabName === 'users') {
                // 检查权限
                if (currentUser && (currentUser.role === 'admin' || currentUser.role === 'owner')) {
                    document.getElementById('user-management-content').classList.add('active');
                    document.querySelector('.settings-tab[onclick*="users"]').classList.add('active');
                    loadUsers();
                } else {
                    // 权限不足，切换回系统设置
                    document.getElementById('system-settings-content').classList.add('active');
                    document.querySelector('.settings-tab[onclick*="system"]').classList.add('active');
                    showMessage('settings-message', '权限不足，无法访问用户管理', 'error');
                    return;
                }
            }
        }

        // 添加积分
        async function addPoints() {
            const phone = document.getElementById('sales-phone').value.trim();
            const amount = parseFloat(document.getElementById('sales-amount').value);
            const ratioType = document.getElementById('points-ratio').value;

            if (!phone || !amount || amount <= 0) {
                showMessage('sales-message', '请填写完整信息', 'error');
                return;
            }

            try {
                const response = await apiRequest('/points/add', {
                    method: 'POST',
                    body: JSON.stringify({ phone, amount, ratioType })
                });

                if (response.success) {
                    showMessage('sales-message', response.message, 'success');

                    // 清空表单
                    document.getElementById('sales-phone').value = '';
                    document.getElementById('sales-amount').value = '';

                    // 重新加载数据
                    await loadMembers();
                    await loadTransactions();
                    updateMemberStats();
                    displayMembers();
                }
            } catch (error) {
                showMessage('sales-message', error.message || '添加积分失败', 'error');
            }
        }

        // 查询会员信息
        async function queryMember() {
            const phone = document.getElementById('redeem-phone').value.trim();

            if (!phone) {
                showMessage('redeem-message', '请输入手机号', 'error');
                return;
            }

            try {
                const response = await apiRequest(`/members/${phone}`);

                if (response.success) {
                    const member = response.member;
                    document.getElementById('member-display').innerHTML = `
                        <div class="member-info">
                            <h3>${member.name}</h3>
                            <p>手机号：${phone}</p>
                            <div class="points-display">${member.points}</div>
                            <p>可用积分</p>
                        </div>
                    `;

                    document.getElementById('redeem-message').innerHTML = '';
                }
            } catch (error) {
                document.getElementById('member-display').innerHTML = '';
                showMessage('redeem-message', error.message || '查询失败', 'error');
            }
        }

        // 更新代金券显示
        function updateVoucherDisplay() {
            const voucherGrid = document.getElementById('voucher-grid');
            voucherGrid.innerHTML = '';
            
            if (systemSettings.customVouchers.length === 0) {
                voucherGrid.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无可用代金券，请在设置中添加</p>';
                return;
            }
            
            // 显示所有自定义代金券
            systemSettings.customVouchers.forEach(voucher => {
                const voucherCard = document.createElement('div');
                voucherCard.className = 'voucher-card';
                voucherCard.onclick = () => redeemVoucher(voucher.amount, voucher.points);
                voucherCard.innerHTML = `
                    <h4>${voucher.amount}元代金券</h4>
                    <p>需要${voucher.points}积分</p>
                `;
                voucherGrid.appendChild(voucherCard);
            });
        }

        // 更新积分比例选项
        function updatePointsRatioOptions() {
            const ratioSelect = document.getElementById('points-ratio');
            const options = ratioSelect.querySelectorAll('option');
            options[0].textContent = `默认会员比例 (1元=${systemSettings.defaultPointsRatio}积分)`;
            options[1].textContent = `VIP会员比例 (1元=${systemSettings.vipPointsRatio}积分)`;
        }

        // 保存积分比例设置
        async function savePointsRatio() {
            const defaultRatio = parseFloat(document.getElementById('default-points-ratio').value);
            const vipRatio = parseFloat(document.getElementById('vip-points-ratio').value);

            if (!defaultRatio || defaultRatio <= 0 || !vipRatio || vipRatio <= 0) {
                showMessage('settings-message', '请输入有效的积分比例', 'error');
                return;
            }

            try {
                const response = await apiRequest('/settings', {
                    method: 'PUT',
                    body: JSON.stringify({
                        defaultPointsRatio: defaultRatio,
                        vipPointsRatio: vipRatio
                    })
                });

                if (response.success) {
                    systemSettings.defaultPointsRatio = defaultRatio;
                    systemSettings.vipPointsRatio = vipRatio;

                    showMessage('settings-message', response.message, 'success');
                    updatePointsRatioOptions();
                }
            } catch (error) {
                showMessage('settings-message', error.message || '保存失败', 'error');
            }
        }



        // 添加自定义代金券
        async function addCustomVoucher() {
            const amount = parseInt(document.getElementById('custom-voucher-amount').value);
            const points = parseInt(document.getElementById('custom-voucher-points').value);

            if (!amount || !points || amount <= 0 || points <= 0) {
                showMessage('settings-message', '请输入有效的代金券金额和积分', 'error');
                return;
            }

            try {
                const response = await apiRequest('/vouchers', {
                    method: 'POST',
                    body: JSON.stringify({ amount, points })
                });

                if (response.success) {
                    document.getElementById('custom-voucher-amount').value = '';
                    document.getElementById('custom-voucher-points').value = '';

                    showMessage('settings-message', `已添加${amount}元代金券 (${points}积分)`, 'success');

                    // 重新加载代金券数据
                    await loadVouchers();
                    displayAllVouchers();
                    updateVoucherDisplay();
                }
            } catch (error) {
                showMessage('settings-message', error.message || '添加代金券失败', 'error');
            }
        }

        // 显示所有代金券列表
        function displayAllVouchers() {
            const container = document.getElementById('all-vouchers-list');
            container.innerHTML = '';

            if (systemSettings.customVouchers.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">暂无代金券，请添加新的代金券</p>';
                return;
            }

            systemSettings.customVouchers.forEach((voucher) => {
                const item = document.createElement('div');
                item.className = 'custom-voucher-item';
                item.innerHTML = `
                    <div class="custom-voucher-info">
                        <div><strong>${voucher.amount}元代金券</strong></div>
                        <div><span>需要 ${voucher.points} 积分</span></div>
                    </div>
                    <div class="voucher-actions">
                        <button class="custom-voucher-edit" onclick="editVoucher(${voucher.id})">编辑</button>
                        <button class="custom-voucher-remove" onclick="removeCustomVoucher(${voucher.id})">删除</button>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // 编辑代金券
        function editVoucher(voucherId) {
            const voucher = systemSettings.customVouchers.find(v => v.id === voucherId);
            if (!voucher) return;

            const container = document.getElementById('all-vouchers-list');
            const items = container.children;

            // 找到对应的DOM元素
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                if (item.querySelector(`[onclick*="${voucherId}"]`)) {
                    item.innerHTML = `
                        <div class="edit-voucher-form">
                            <span>金额:</span>
                            <input type="number" id="edit-amount-${voucherId}" value="${voucher.amount}" min="1">
                            <span>积分:</span>
                            <input type="number" id="edit-points-${voucherId}" value="${voucher.points}" min="1">
                        </div>
                        <div class="voucher-actions">
                            <button class="save-btn" onclick="saveVoucherEdit(${voucherId})">保存</button>
                            <button class="cancel-btn" onclick="displayAllVouchers()">取消</button>
                        </div>
                    `;
                    break;
                }
            }
        }

        // 保存编辑的代金券
        async function saveVoucherEdit(voucherId) {
            const newAmount = parseInt(document.getElementById(`edit-amount-${voucherId}`).value);
            const newPoints = parseInt(document.getElementById(`edit-points-${voucherId}`).value);

            if (!newAmount || !newPoints || newAmount <= 0 || newPoints <= 0) {
                showMessage('settings-message', '请输入有效的金额和积分', 'error');
                return;
            }

            try {
                const response = await apiRequest(`/vouchers/${voucherId}`, {
                    method: 'PUT',
                    body: JSON.stringify({ amount: newAmount, points: newPoints })
                });

                if (response.success) {
                    showMessage('settings-message', '代金券修改成功', 'success');

                    // 重新加载代金券数据
                    await loadVouchers();
                    displayAllVouchers();
                    updateVoucherDisplay();
                }
            } catch (error) {
                showMessage('settings-message', error.message || '修改代金券失败', 'error');
            }
        }

        // 删除自定义代金券
        async function removeCustomVoucher(voucherId) {
            if (confirm('确定要删除这个代金券吗？')) {
                try {
                    const response = await apiRequest(`/vouchers/${voucherId}`, {
                        method: 'DELETE'
                    });

                    if (response.success) {
                        showMessage('settings-message', response.message, 'success');

                        // 重新加载代金券数据
                        await loadVouchers();
                        displayAllVouchers();
                        updateVoucherDisplay();
                    }
                } catch (error) {
                    showMessage('settings-message', error.message || '删除代金券失败', 'error');
                }
            }
        }

        // 加载设置页面的值
        function loadSettingsValues() {
            document.getElementById('default-points-ratio').value = systemSettings.defaultPointsRatio;
            document.getElementById('vip-points-ratio').value = systemSettings.vipPointsRatio;
            
            displayAllVouchers();
        }

        // 导出数据为Excel格式
        async function exportData() {
            try {
                const response = await apiRequest('/export');

                if (response.success) {
                    const data = response.data;

                    // 创建工作簿
                    const workbook = XLSX.utils.book_new();

                    // 会员数据工作表
                    if (data.members && data.members.length > 0) {
                        const membersData = data.members.map(member => ({
                            '姓名': member.name,
                            '手机号': member.phone,
                            '积分': member.points,
                            '加入日期': new Date(member.join_date).toLocaleDateString()
                        }));
                        const membersSheet = XLSX.utils.json_to_sheet(membersData);
                        XLSX.utils.book_append_sheet(workbook, membersSheet, '会员信息');
                    }

                    // 交易记录工作表
                    if (data.transactions && data.transactions.length > 0) {
                        const transactionsData = data.transactions.map(transaction => ({
                            '类型': transaction.type === 'earn' ? '获得积分' : '兑换积分',
                            '会员姓名': transaction.member_name,
                            '会员手机': transaction.member_phone,
                            '金额': transaction.amount || '',
                            '积分': transaction.points,
                            '代金券金额': transaction.voucher_amount || '',
                            '积分比例': transaction.ratio || '',
                            '交易时间': new Date(transaction.created_at).toLocaleString()
                        }));
                        const transactionsSheet = XLSX.utils.json_to_sheet(transactionsData);
                        XLSX.utils.book_append_sheet(workbook, transactionsSheet, '交易记录');
                    }

                    // 代金券设置工作表
                    if (data.vouchers && data.vouchers.length > 0) {
                        const vouchersData = data.vouchers.map(voucher => ({
                            '代金券金额': voucher.amount,
                            '所需积分': voucher.points
                        }));
                        const vouchersSheet = XLSX.utils.json_to_sheet(vouchersData);
                        XLSX.utils.book_append_sheet(workbook, vouchersSheet, '代金券设置');
                    }

                    // 系统设置工作表
                    if (data.settings) {
                        const settingsData = [
                            { '设置项': '默认积分比例', '值': data.settings.default_points_ratio },
                            { '设置项': 'VIP积分比例', '值': data.settings.vip_points_ratio }
                        ];
                        const settingsSheet = XLSX.utils.json_to_sheet(settingsData);
                        XLSX.utils.book_append_sheet(workbook, settingsSheet, '系统设置');
                    }

                    // 生成Excel文件
                    const fileName = `欧妮家会员积分数据_${new Date().toISOString().split('T')[0]}.xlsx`;
                    XLSX.writeFile(workbook, fileName);

                    showMessage('settings-message', 'Excel数据导出成功', 'success');
                }
            } catch (error) {
                console.error('导出数据错误:', error);
                showMessage('settings-message', error.message || '导出数据失败', 'error');
            }
        }

        // 清空所有数据 - 三次确认
        async function clearAllData() {
            // 第一次确认
            if (!confirm('⚠️ 警告：您即将清空所有数据！\n\n这将删除：\n• 所有会员信息\n• 所有交易记录\n• 所有代金券设置\n\n确定要继续吗？')) {
                return;
            }

            // 第二次确认
            if (!confirm('🚨 再次确认：清空所有数据后无法恢复！\n\n建议您先导出数据进行备份。\n\n您真的确定要清空所有数据吗？')) {
                return;
            }

            // 第三次确认 - 需要输入确认文字
            const confirmText = prompt('🔴 最后确认：请输入 "清空数据" 来确认此操作：\n\n注意：此操作将永久删除所有数据，无法恢复！');

            if (confirmText !== '清空数据') {
                if (confirmText !== null) { // 用户没有点击取消
                    showMessage('settings-message', '确认文字不正确，操作已取消', 'error');
                }
                return;
            }

            try {
                // 显示正在处理的消息
                showMessage('settings-message', '正在清空数据，请稍候...', 'success');

                const response = await apiRequest('/clear-all', {
                    method: 'DELETE'
                });

                if (response.success) {
                    showMessage('settings-message', '✅ ' + response.message, 'success');

                    // 重新加载所有数据
                    await loadMembers();
                    await loadTransactions();
                    await loadVouchers();
                    updateMemberStats();
                    displayMembers();
                    displayTransactionHistory();
                    displayAllVouchers();
                    updateVoucherDisplay();
                }
            } catch (error) {
                showMessage('settings-message', '❌ ' + (error.message || '清空数据失败'), 'error');
            }
        }
        async function redeemVoucher(voucherAmount, requiredPoints) {
            const phone = document.getElementById('redeem-phone').value.trim();

            if (!phone) {
                showMessage('redeem-message', '请先查询会员信息', 'error');
                return;
            }

            try {
                const response = await apiRequest('/points/redeem', {
                    method: 'POST',
                    body: JSON.stringify({ phone, voucherAmount, requiredPoints })
                });

                if (response.success) {
                    showMessage('redeem-message', response.message, 'success');

                    // 重新查询会员信息
                    await queryMember();

                    // 重新加载数据
                    await loadMembers();
                    await loadTransactions();
                    updateMemberStats();
                    displayMembers();
                }
            } catch (error) {
                showMessage('redeem-message', error.message || '兑换失败', 'error');
            }
        }

        // 添加会员
        async function addMember() {
            const name = document.getElementById('member-name').value.trim();
            const phone = document.getElementById('member-phone').value.trim();

            if (!name || !phone) {
                showMessage('member-message', '请填写完整信息', 'error');
                return;
            }

            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showMessage('member-message', '请输入正确的手机号', 'error');
                return;
            }

            try {
                const response = await apiRequest('/members', {
                    method: 'POST',
                    body: JSON.stringify({ name, phone })
                });

                if (response.success) {
                    showMessage('member-message', `成功添加会员：${name}`, 'success');

                    // 清空表单
                    document.getElementById('member-name').value = '';
                    document.getElementById('member-phone').value = '';

                    // 重新加载数据
                    await loadMembers();
                    updateMemberStats();
                    displayMembers();
                }
            } catch (error) {
                showMessage('member-message', error.message || '添加会员失败', 'error');
            }
        }

        // 更新会员统计
        async function updateMemberStats() {
            try {
                const response = await apiRequest('/stats');
                if (response.success) {
                    document.getElementById('total-members').textContent = response.stats.totalMembers;
                    document.getElementById('total-points').textContent = response.stats.totalPoints;
                } else {
                    // 如果API失败，使用本地数据
                    const totalMembers = Object.keys(members).length;
                    const totalPoints = Object.values(members).reduce((sum, member) => sum + member.points, 0);

                    document.getElementById('total-members').textContent = totalMembers;
                    document.getElementById('total-points').textContent = totalPoints;
                }
            } catch (error) {
                console.error('获取统计信息失败:', error);
                // 使用本地数据作为备用
                const totalMembers = Object.keys(members).length;
                const totalPoints = Object.values(members).reduce((sum, member) => sum + member.points, 0);

                document.getElementById('total-members').textContent = totalMembers;
                document.getElementById('total-points').textContent = totalPoints;
            }
        }

        // 显示会员列表
        function displayMembers() {
            const memberList = document.getElementById('member-list');
            memberList.innerHTML = '';

            Object.entries(members).forEach(([phone, member]) => {
                const memberItem = document.createElement('div');
                memberItem.className = 'member-item';

                // 检查当前用户是否为系统管理员
                const isAdmin = currentUser && currentUser.role === 'admin';

                memberItem.innerHTML = `
                    <div class="member-details">
                        <div class="member-name">${member.name}</div>
                        <div class="member-phone">${phone}</div>
                        ${isAdmin ? `
                            <div class="member-actions">
                                <button class="member-edit-btn" onclick="editMember('${phone}', '${member.name}', ${member.points})">编辑</button>
                                <button class="member-delete-btn" onclick="deleteMember('${phone}', '${member.name}')">删除</button>
                            </div>
                        ` : ''}
                    </div>
                    <div class="member-points">${member.points} 积分</div>
                `;
                memberList.appendChild(memberItem);
            });
        }

        // 编辑会员信息
        function editMember(phone, currentName, currentPoints) {
            // 创建编辑表单模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 400px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">编辑会员信息</h3>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">手机号</label>
                        <input type="text" id="edit-member-phone" value="${phone}" disabled
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; background: #f5f5f5; color: #999;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">姓名</label>
                        <input type="text" id="edit-member-name" value="${currentName}"
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">积分</label>
                        <input type="number" id="edit-member-points" value="${currentPoints}"
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="closeMemberEditModal()"
                                style="padding: 10px 20px; border: 1px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer;">
                            取消
                        </button>
                        <button onclick="saveMemberEdit('${phone}')"
                                style="padding: 10px 20px; border: none; background: #667eea; color: white; border-radius: 6px; cursor: pointer;">
                            保存
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentMemberEditModal = modal;

            // 点击背景关闭模态框
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeMemberEditModal();
                }
            });
        }

        // 关闭编辑模态框
        function closeMemberEditModal() {
            if (window.currentMemberEditModal) {
                document.body.removeChild(window.currentMemberEditModal);
                window.currentMemberEditModal = null;
            }
        }

        // 保存会员编辑
        async function saveMemberEdit(phone) {
            const newName = document.getElementById('edit-member-name').value.trim();
            const newPoints = parseInt(document.getElementById('edit-member-points').value);

            if (!newName) {
                alert('请输入会员姓名');
                return;
            }

            if (isNaN(newPoints) || newPoints < 0) {
                alert('请输入有效的积分数');
                return;
            }

            try {
                const response = await apiRequest(`/members/${phone}`, {
                    method: 'PUT',
                    body: JSON.stringify({
                        name: newName,
                        points: newPoints
                    })
                });

                if (response.success) {
                    // 更新本地数据
                    members[phone].name = newName;
                    members[phone].points = newPoints;

                    // 刷新显示
                    displayMembers();
                    updateMemberStats();

                    // 关闭模态框
                    closeMemberEditModal();

                    showMessage('member-message', '会员信息更新成功', 'success');
                }
            } catch (error) {
                showMessage('member-message', error.message || '更新会员信息失败', 'error');
            }
        }

        // 删除会员
        async function deleteMember(phone, name) {
            if (!confirm(`确定要删除会员 "${name}" (${phone}) 吗？\n\n此操作将同时删除该会员的所有交易记录，且无法恢复！`)) {
                return;
            }

            // 二次确认
            if (!confirm(`再次确认：您即将删除会员 "${name}"\n\n这是不可逆的操作，确定继续吗？`)) {
                return;
            }

            try {
                const response = await apiRequest(`/members/${phone}`, {
                    method: 'DELETE'
                });

                if (response.success) {
                    // 从本地数据中删除
                    delete members[phone];

                    // 删除相关交易记录
                    transactions = transactions.filter(t => t.phone !== phone);

                    // 刷新显示
                    displayMembers();
                    updateMemberStats();
                    displayTransactionHistory();

                    showMessage('member-message', `会员 "${name}" 删除成功`, 'success');
                }
            } catch (error) {
                showMessage('member-message', error.message || '删除会员失败', 'error');
            }
        }

        // 显示交易记录加载状态
        function showTransactionLoading() {
            const historyContainer = document.getElementById('transaction-history');
            historyContainer.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #666;">
                    <div style="font-size: 18px; margin-bottom: 10px;">⏳</div>
                    <div>正在加载交易记录...</div>
                </div>
            `;
        }

        function hideTransactionLoading() {
            // 加载完成后会调用displayTransactionHistory重新渲染
        }

        function showLoadMoreIndicator() {
            const existingIndicator = document.getElementById('load-more-indicator');
            if (existingIndicator) return;

            const historyContainer = document.getElementById('transaction-history');
            const indicator = document.createElement('div');
            indicator.id = 'load-more-indicator';
            indicator.style.cssText = `
                text-align: center;
                padding: 20px;
                color: #666;
                font-size: 14px;
            `;
            indicator.innerHTML = `
                <div style="margin-bottom: 5px;">⏳</div>
                <div>正在加载更多...</div>
            `;
            historyContainer.appendChild(indicator);
        }

        function hideLoadMoreIndicator() {
            const indicator = document.getElementById('load-more-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // 格式化日期时间
        function formatDateTime(dateString) {
            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    return '无效日期';
                }

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
            } catch (error) {
                return '格式化错误';
            }
        }

        // 搜索交易记录
        let filteredTransactions = []; // 存储过滤后的交易记录
        let currentSearchTerm = ''; // 当前搜索词

        // 搜索防抖定时器
        let searchDebounceTimer = null;

        function searchTransactions() {
            const searchInput = document.getElementById('transaction-search-input');
            const searchTerm = searchInput.value.trim();
            const clearButton = document.getElementById('transaction-search-clear');

            currentSearchTerm = searchTerm;

            // 显示/隐藏清除按钮
            if (searchTerm) {
                clearButton.style.display = 'block';
            } else {
                clearButton.style.display = 'none';
            }

            // 清除之前的定时器
            if (searchDebounceTimer) {
                clearTimeout(searchDebounceTimer);
            }

            // 设置防抖，500ms后执行搜索
            searchDebounceTimer = setTimeout(async () => {
                await performSearch(searchTerm);
            }, 500);
        }

        async function performSearch(searchTerm) {
            const resultsDiv = document.getElementById('transaction-search-results');

            // 重置分页
            transactionPagination.currentPage = 1;
            transactionPagination.hasMore = true;

            try {
                // 显示搜索状态
                if (searchTerm) {
                    resultsDiv.textContent = '正在搜索...';
                    resultsDiv.style.color = '#667eea';
                } else {
                    resultsDiv.textContent = '';
                }

                // 执行搜索
                const success = await loadTransactions(1, searchTerm, false);

                if (success) {
                    // 显示搜索结果统计
                    if (searchTerm) {
                        if (transactionPagination.totalRecords === 0) {
                            resultsDiv.textContent = '未找到匹配的交易记录';
                            resultsDiv.style.color = '#999';
                        } else {
                            resultsDiv.textContent = `找到 ${transactionPagination.totalRecords} 条交易记录`;
                            resultsDiv.style.color = '#667eea';
                        }
                    } else {
                        resultsDiv.textContent = '';
                    }

                    // 显示搜索结果
                    displayTransactionHistory();
                }
            } catch (error) {
                resultsDiv.textContent = '搜索出错，请重试';
                resultsDiv.style.color = '#dc3545';
            }
        }

        async function clearTransactionSearch() {
            const searchInput = document.getElementById('transaction-search-input');
            const clearButton = document.getElementById('transaction-search-clear');
            const resultsDiv = document.getElementById('transaction-search-results');

            searchInput.value = '';
            clearButton.style.display = 'none';
            resultsDiv.textContent = '';
            currentSearchTerm = '';

            // 清除搜索防抖定时器
            if (searchDebounceTimer) {
                clearTimeout(searchDebounceTimer);
            }

            // 重置分页并重新加载所有记录
            transactionPagination.currentPage = 1;
            transactionPagination.hasMore = true;

            await loadTransactions(1, '', false);
            displayTransactionHistory();

            // 聚焦到搜索框
            searchInput.focus();
        }

        function handleSearchKeyup(event) {
            // 按ESC键清除搜索
            if (event.key === 'Escape') {
                clearTransactionSearch();
            }
        }

        // 显示交易记录
        function displayTransactionHistory() {
            const historyContainer = document.getElementById('transaction-history');

            // 移除加载指示器
            hideLoadMoreIndicator();

            // 如果是第一次加载，清空容器
            if (transactionPagination.currentPage === 1) {
                historyContainer.innerHTML = '';
            }

            if (transactions.length === 0) {
                historyContainer.innerHTML = '<p style="text-align: center; color: #666; margin-top: 50px;">暂无交易记录</p>';
                return;
            }

            // 检查当前用户是否为系统管理员
            const isAdmin = currentUser && currentUser.role === 'admin';

            // 使用当前加载的交易记录
            const sortedTransactions = transactions;

            sortedTransactions.forEach((transaction, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';

                let content = `
                    <h4>${transaction.type}</h4>
                    <p>会员：${transaction.name} (${transaction.phone})</p>
                    <p>时间：${transaction.timestamp}</p>
                `;

                if (transaction.type === '积分获得') {
                    content += `
                        <p>销售金额：${transaction.amount}元</p>
                        <p>积分比例：${transaction.ratio || '1元=1积分'}</p>
                        <p style="color: #28a745; font-weight: bold;">+${transaction.points} 积分</p>
                    `;
                } else if (transaction.type === '积分兑换') {
                    content += `
                        <p>兑换：${transaction.voucherAmount}元代金券</p>
                        <p style="color: #dc3545; font-weight: bold;">${transaction.points} 积分</p>
                    `;
                }

                // 为系统管理员添加编辑和删除按钮
                if (isAdmin) {
                    // 找到在原始transactions数组中的索引
                    const originalIndex = transactions.findIndex(t => t.id === transaction.id);
                    content += `
                        <div class="transaction-actions">
                            <button class="transaction-edit-btn" onclick="editTransaction(${originalIndex})">编辑</button>
                            <button class="transaction-delete-btn" onclick="deleteTransaction(${originalIndex})">删除</button>
                        </div>
                    `;
                }

                historyItem.innerHTML = content;
                historyContainer.appendChild(historyItem);
            });

            // 添加加载更多按钮或完成提示
            addLoadMoreSection();

            // 设置无限滚动监听
            setupInfiniteScroll();
        }

        function addLoadMoreSection() {
            const historyContainer = document.getElementById('transaction-history');
            const existingSection = document.getElementById('load-more-section');

            // 移除现有的加载更多区域
            if (existingSection) {
                existingSection.remove();
            }

            const loadMoreSection = document.createElement('div');
            loadMoreSection.id = 'load-more-section';
            loadMoreSection.style.cssText = `
                text-align: center;
                padding: 30px 20px;
                border-top: 1px solid #eee;
                margin-top: 20px;
            `;

            if (transactionPagination.hasMore) {
                // 还有更多数据
                loadMoreSection.innerHTML = `
                    <button onclick="loadMoreTransactions()"
                            style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 14px;">
                        加载更多 (${transactionPagination.currentPage}/${transactionPagination.totalPages})
                    </button>
                    <div style="margin-top: 10px; font-size: 12px; color: #666;">
                        已显示 ${transactions.length} / ${transactionPagination.totalRecords} 条记录
                    </div>
                `;
            } else if (transactions.length > 0) {
                // 已加载完所有数据
                loadMoreSection.innerHTML = `
                    <div style="color: #666; font-size: 14px;">
                        ✅ 已显示全部 ${transactionPagination.totalRecords} 条交易记录
                    </div>
                `;
            }

            historyContainer.appendChild(loadMoreSection);
        }

        function setupInfiniteScroll() {
            const historyContainer = document.getElementById('transaction-history');

            // 移除之前的滚动监听
            if (window.infiniteScrollHandler) {
                historyContainer.removeEventListener('scroll', window.infiniteScrollHandler);
            }

            // 创建新的滚动监听
            window.infiniteScrollHandler = function() {
                const { scrollTop, scrollHeight, clientHeight } = historyContainer;

                // 当滚动到距离底部100px时触发加载
                if (scrollTop + clientHeight >= scrollHeight - 100) {
                    if (transactionPagination.hasMore && !transactionPagination.loading) {
                        loadMoreTransactions();
                    }
                }
            };

            historyContainer.addEventListener('scroll', window.infiniteScrollHandler);
        }

        // 编辑交易记录
        function editTransaction(index) {
            const transaction = transactions[index];

            // 创建编辑表单模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            const isEarnType = transaction.type === '积分获得';

            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 15px; width: 90%; max-width: 500px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h3 style="margin-bottom: 20px; color: #333; text-align: center;">编辑交易记录</h3>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">交易类型</label>
                        <input type="text" value="${transaction.type}" disabled
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; background: #f5f5f5; color: #999;">
                    </div>

                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">会员信息</label>
                        <input type="text" value="${transaction.name} (${transaction.phone})" disabled
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; background: #f5f5f5; color: #999;">
                    </div>

                    ${isEarnType ? `
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">销售金额（元）</label>
                            <input type="number" id="edit-transaction-amount" value="${transaction.amount}" step="0.01"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    ` : `
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">代金券金额（元）</label>
                            <input type="number" id="edit-transaction-voucher" value="${transaction.voucherAmount}" step="0.01"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    `}

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; color: #666; font-weight: 500;">积分</label>
                        <input type="number" id="edit-transaction-points" value="${Math.abs(transaction.points)}"
                               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="closeTransactionEditModal()"
                                style="padding: 10px 20px; border: 1px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer;">
                            取消
                        </button>
                        <button onclick="saveTransactionEdit(${index})"
                                style="padding: 10px 20px; border: none; background: #667eea; color: white; border-radius: 6px; cursor: pointer;">
                            保存
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            window.currentTransactionEditModal = modal;

            // 点击背景关闭模态框
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeTransactionEditModal();
                }
            });
        }

        // 关闭交易记录编辑模态框
        function closeTransactionEditModal() {
            if (window.currentTransactionEditModal) {
                document.body.removeChild(window.currentTransactionEditModal);
                window.currentTransactionEditModal = null;
            }
        }

        // 保存交易记录编辑
        async function saveTransactionEdit(index) {
            const transaction = transactions[index];


            const newPoints = parseInt(document.getElementById('edit-transaction-points').value);

            if (isNaN(newPoints) || newPoints <= 0) {
                alert('请输入有效的积分数');
                return;
            }

            let updateData;

            if (transaction.type === '积分获得') {
                const newAmount = parseFloat(document.getElementById('edit-transaction-amount').value);
                if (isNaN(newAmount) || newAmount <= 0) {
                    alert('请输入有效的销售金额');
                    return;
                }
                updateData = {
                    points: newPoints,
                    amount: newAmount
                };
            } else {
                const newVoucherAmount = parseFloat(document.getElementById('edit-transaction-voucher').value);
                if (isNaN(newVoucherAmount) || newVoucherAmount <= 0) {
                    alert('请输入有效的代金券金额');
                    return;
                }
                updateData = {
                    points: -newPoints, // 积分兑换存储为负数
                    voucher_amount: newVoucherAmount
                };
            }



            try {
                // 显示正在保存的消息
                showMessage('history-message', '正在保存...', 'success');

                // 调用后端API更新交易记录
                const response = await apiRequest(`/transactions/${transaction.id}`, {
                    method: 'PUT',
                    body: JSON.stringify(updateData)
                });



                if (response.success) {
                    // 关闭模态框
                    closeTransactionEditModal();

                    // 重新加载数据
                    await loadMembers();

                    // 重新加载当前页面的交易记录
                    transactionPagination.currentPage = 1;
                    await loadTransactions(1, currentSearchTerm, false);

                    // 刷新显示
                    displayTransactionHistory();
                    displayMembers();
                    updateMemberStats();

                    showMessage('history-message', '✅ 交易记录更新成功', 'success');
                } else {
                    showMessage('history-message', '❌ ' + (response.error || '更新失败'), 'error');
                }
            } catch (error) {
                console.error('保存交易记录错误:', error);
                showMessage('history-message', '❌ ' + (error.message || '更新交易记录失败'), 'error');
            }
        }

        // 删除交易记录
        async function deleteTransaction(index) {
            const transaction = transactions[index];

            if (!confirm(`确定要删除这条交易记录吗？\n\n会员：${transaction.name}\n类型：${transaction.type}\n积分：${transaction.points}\n时间：${transaction.timestamp}\n\n此操作无法恢复！`)) {
                return;
            }

            try {
                // 调用后端API删除交易记录
                const response = await apiRequest(`/transactions/${transaction.id}`, {
                    method: 'DELETE'
                });

                if (response.success) {
                    // 重新加载数据
                    await loadMembers();

                    // 重新加载当前页面的交易记录
                    transactionPagination.currentPage = 1;
                    await loadTransactions(1, currentSearchTerm, false);

                    // 刷新显示
                    displayTransactionHistory();
                    displayMembers();
                    updateMemberStats();

                    showMessage('history-message', '交易记录删除成功', 'success');
                }
            } catch (error) {
                showMessage('history-message', error.message || '删除交易记录失败', 'error');
            }
        }

        // 显示消息
        function showMessage(elementId, message, type) {
            const messageElement = document.getElementById(elementId);
            messageElement.innerHTML = `
                <div class="alert alert-${type === 'error' ? 'error' : 'success'}">
                    ${message}
                </div>
            `;
            
            // 3秒后自动清除消息
            setTimeout(() => {
                messageElement.innerHTML = '';
            }, 3000);
        }

        // 初始化示例数据
        function initializeData() {
            // 添加示例会员
            members['13800138001'] = {
                name: '张小姐',
                points: 1200,
                joinDate: '2024-01-15'
            };
            
            members['13900139002'] = {
                name: '李女士',
                points: 800,
                joinDate: '2024-02-20'
            };
            
            // 添加示例交易记录
            transactions.push({
                type: '积分获得',
                phone: '13800138001',
                name: '张小姐',
                amount: 299,
                points: 299,
                timestamp: '2024-07-25 14:30:00'
            });
            
            transactions.push({
                type: '积分兑换',
                phone: '13900139002',
                name: '李女士',
                voucherAmount: 50,
                points: -500,
                timestamp: '2024-07-24 16:20:00'
            });
        }

        // 页面加载完成后初始化
        window.onload = async function() {
            // 检查是否已登录
            const savedToken = localStorage.getItem('authToken');
            const savedUser = localStorage.getItem('currentUser');

            if (savedToken && savedUser) {
                try {
                    authToken = savedToken;
                    currentUser = JSON.parse(savedUser);
                    isLoggedIn = true;

                    document.getElementById('current-user').textContent = `欢迎，${currentUser.name}`;
                    document.getElementById('login-page').classList.add('hidden');
                    document.getElementById('main-app').classList.remove('hidden');

                    await initializeApp();
                    return;
                } catch (e) {
                    console.error('恢复登录状态失败:', e);
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('currentUser');
                }
            }

            // 默认显示登录页面
            document.getElementById('login-page').classList.remove('hidden');
            document.getElementById('main-app').classList.add('hidden');
        };
    </script>
</body>
</html>