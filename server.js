const express = require('express');
const cors = require('cors');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const path = require('path');
require('dotenv').config();

const { pool, initDatabase } = require('./database');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// JWT验证中间件
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: '需要登录' });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: '无效的令牌' });
        }
        req.user = user;
        next();
    });
};

// 登录接口
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.status(400).json({ error: '请输入用户名和密码' });
        }

        const result = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
        
        if (result.rows.length === 0) {
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        const user = result.rows[0];
        const validPassword = await bcrypt.compare(password, user.password);

        if (!validPassword) {
            return res.status(401).json({ error: '用户名或密码错误' });
        }

        // 更新最后登录时间
        await pool.query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1', [user.id]);

        const token = jwt.sign(
            { id: user.id, username: user.username, role: user.role },
            process.env.JWT_SECRET,
            { expiresIn: '24h' }
        );

        res.json({
            success: true,
            token,
            user: {
                id: user.id,
                username: user.username,
                name: user.name,
                role: user.role
            }
        });
    } catch (error) {
        console.error('登录错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取用户列表
app.get('/api/users', authenticateToken, async (req, res) => {
    try {
        // 只有管理员和店主可以查看用户列表
        if (req.user.role !== 'admin' && req.user.role !== 'owner') {
            return res.status(403).json({ error: '权限不足' });
        }

        const result = await pool.query('SELECT id, username, name, role, created_at FROM users ORDER BY created_at DESC');
        res.json({ success: true, users: result.rows });
    } catch (error) {
        console.error('获取用户列表错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 添加新用户
app.post('/api/users', authenticateToken, async (req, res) => {
    try {
        // 只有管理员和店主可以添加用户
        if (req.user.role !== 'admin' && req.user.role !== 'owner') {
            return res.status(403).json({ error: '权限不足' });
        }

        const { username, password, name, role } = req.body;

        if (!username || !password || !name || !role) {
            return res.status(400).json({ error: '请填写完整信息' });
        }

        if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
            return res.status(400).json({ error: '用户名只能包含字母、数字和下划线，长度3-20位' });
        }

        if (password.length < 6) {
            return res.status(400).json({ error: '密码长度至少6位' });
        }

        if (!['admin', 'staff', 'owner'].includes(role)) {
            return res.status(400).json({ error: '无效的角色' });
        }

        // 检查用户名是否已存在
        const existing = await pool.query('SELECT id FROM users WHERE username = $1', [username]);
        if (existing.rows.length > 0) {
            return res.status(400).json({ error: '用户名已存在' });
        }

        const hashedPassword = await bcrypt.hash(password, 10);
        const result = await pool.query(
            'INSERT INTO users (username, password, name, role) VALUES ($1, $2, $3, $4) RETURNING id, username, name, role, created_at',
            [username, hashedPassword, name, role]
        );

        res.json({ success: true, user: result.rows[0] });
    } catch (error) {
        console.error('添加用户错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新用户
app.put('/api/users/:id', authenticateToken, async (req, res) => {
    try {
        // 只有管理员和店主可以更新用户
        if (req.user.role !== 'admin' && req.user.role !== 'owner') {
            return res.status(403).json({ error: '权限不足' });
        }

        const { id } = req.params;
        const { name, role, password } = req.body;

        if (!name || !role) {
            return res.status(400).json({ error: '请填写完整信息' });
        }

        if (!['admin', 'staff', 'owner'].includes(role)) {
            return res.status(400).json({ error: '无效的角色' });
        }

        let query = 'UPDATE users SET name = $1, role = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING id, username, name, role, last_login, created_at';
        let params = [name, role, id];

        // 如果提供了新密码，则更新密码
        if (password && password.trim()) {
            if (password.length < 6) {
                return res.status(400).json({ error: '密码长度至少6位' });
            }
            const hashedPassword = await bcrypt.hash(password, 10);
            query = 'UPDATE users SET name = $1, role = $2, password = $4, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING id, username, name, role, last_login, created_at';
            params = [name, role, id, hashedPassword];
        }

        const result = await pool.query(query, params);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }

        res.json({ success: true, user: result.rows[0] });
    } catch (error) {
        console.error('更新用户错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除用户
app.delete('/api/users/:id', authenticateToken, async (req, res) => {
    try {
        // 只有管理员和店主可以删除用户
        if (req.user.role !== 'admin' && req.user.role !== 'owner') {
            return res.status(403).json({ error: '权限不足' });
        }

        const { id } = req.params;

        // 不能删除自己
        if (parseInt(id) === req.user.id) {
            return res.status(400).json({ error: '不能删除自己的账户' });
        }

        const result = await pool.query('DELETE FROM users WHERE id = $1 RETURNING username', [id]);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: '用户不存在' });
        }

        res.json({ success: true, message: '用户删除成功' });
    } catch (error) {
        console.error('删除用户错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取会员列表
app.get('/api/members', authenticateToken, async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM members ORDER BY created_at DESC');
        res.json({ success: true, members: result.rows });
    } catch (error) {
        console.error('获取会员列表错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 添加会员
app.post('/api/members', authenticateToken, async (req, res) => {
    try {
        const { name, phone } = req.body;

        if (!name || !phone) {
            return res.status(400).json({ error: '请填写完整信息' });
        }

        if (!/^1[3-9]\d{9}$/.test(phone)) {
            return res.status(400).json({ error: '请输入正确的手机号' });
        }

        // 检查手机号是否已存在
        const existing = await pool.query('SELECT id FROM members WHERE phone = $1', [phone]);
        if (existing.rows.length > 0) {
            return res.status(400).json({ error: '该手机号已存在' });
        }

        const result = await pool.query(
            'INSERT INTO members (name, phone) VALUES ($1, $2) RETURNING *',
            [name, phone]
        );

        res.json({ success: true, member: result.rows[0] });
    } catch (error) {
        console.error('添加会员错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取会员信息
app.get('/api/members/:phone', authenticateToken, async (req, res) => {
    try {
        const { phone } = req.params;
        const result = await pool.query('SELECT * FROM members WHERE phone = $1', [phone]);
        
        if (result.rows.length === 0) {
            return res.status(404).json({ error: '会员不存在' });
        }

        res.json({ success: true, member: result.rows[0] });
    } catch (error) {
        console.error('获取会员信息错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新会员信息
app.put('/api/members/:phone', authenticateToken, async (req, res) => {
    try {
        const { phone } = req.params;
        const { name, points } = req.body;

        // 检查权限 - 只有系统管理员可以编辑会员
        if (req.user.role !== 'admin') {
            return res.status(403).json({ error: '权限不足，只有系统管理员可以编辑会员信息' });
        }

        if (!name || points === undefined || points < 0) {
            return res.status(400).json({ error: '请填写完整且有效的信息' });
        }

        // 检查会员是否存在
        const checkResult = await pool.query('SELECT * FROM members WHERE phone = $1', [phone]);
        if (checkResult.rows.length === 0) {
            return res.status(404).json({ error: '会员不存在' });
        }

        // 更新会员信息
        const result = await pool.query(
            'UPDATE members SET name = $1, points = $2, updated_at = CURRENT_TIMESTAMP WHERE phone = $3 RETURNING *',
            [name, points, phone]
        );

        res.json({
            success: true,
            message: '会员信息更新成功',
            member: result.rows[0]
        });
    } catch (error) {
        console.error('更新会员信息错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除会员
app.delete('/api/members/:phone', authenticateToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const { phone } = req.params;

        // 检查权限 - 只有系统管理员可以删除会员
        if (req.user.role !== 'admin') {
            return res.status(403).json({ error: '权限不足，只有系统管理员可以删除会员' });
        }

        // 检查会员是否存在
        const checkResult = await client.query('SELECT * FROM members WHERE phone = $1', [phone]);
        if (checkResult.rows.length === 0) {
            return res.status(404).json({ error: '会员不存在' });
        }

        const memberName = checkResult.rows[0].name;

        // 删除相关交易记录
        await client.query('DELETE FROM transactions WHERE member_phone = $1', [phone]);

        // 删除会员
        await client.query('DELETE FROM members WHERE phone = $1', [phone]);

        await client.query('COMMIT');

        res.json({
            success: true,
            message: `会员 "${memberName}" 及其相关数据删除成功`
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('删除会员错误:', error);
        res.status(500).json({ error: '服务器错误' });
    } finally {
        client.release();
    }
});

// 添加积分
app.post('/api/points/add', authenticateToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const { phone, amount, ratioType } = req.body;

        if (!phone || !amount || amount <= 0) {
            return res.status(400).json({ error: '请填写完整信息' });
        }

        // 获取会员信息
        const memberResult = await client.query('SELECT * FROM members WHERE phone = $1', [phone]);
        if (memberResult.rows.length === 0) {
            return res.status(404).json({ error: '会员不存在' });
        }

        // 获取积分比例
        const ratioKey = ratioType === 'vip' ? 'vip_points_ratio' : 'default_points_ratio';
        const ratioResult = await client.query('SELECT setting_value FROM system_settings WHERE setting_key = $1', [ratioKey]);
        const ratio = parseFloat(ratioResult.rows[0]?.setting_value || '1');

        const points = Math.floor(amount * ratio);
        const member = memberResult.rows[0];

        // 更新会员积分
        await client.query(
            'UPDATE members SET points = points + $1, updated_at = CURRENT_TIMESTAMP WHERE phone = $2',
            [points, phone]
        );

        // 记录交易
        await client.query(
            'INSERT INTO transactions (type, member_phone, member_name, amount, points, ratio) VALUES ($1, $2, $3, $4, $5, $6)',
            ['积分获得', phone, member.name, amount, points, `1元=${ratio}积分`]
        );

        await client.query('COMMIT');

        res.json({
            success: true,
            message: `成功为 ${member.name} 添加 ${points} 积分`,
            points: points
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('添加积分错误:', error);
        res.status(500).json({ error: '服务器错误' });
    } finally {
        client.release();
    }
});

// 兑换代金券
app.post('/api/points/redeem', authenticateToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const { phone, voucherAmount, requiredPoints } = req.body;

        // 获取会员信息
        const memberResult = await client.query('SELECT * FROM members WHERE phone = $1', [phone]);
        if (memberResult.rows.length === 0) {
            return res.status(404).json({ error: '会员不存在' });
        }

        const member = memberResult.rows[0];

        if (member.points < requiredPoints) {
            return res.status(400).json({ 
                error: `积分不足，需要 ${requiredPoints} 积分，当前只有 ${member.points} 积分` 
            });
        }

        // 扣除积分
        await client.query(
            'UPDATE members SET points = points - $1, updated_at = CURRENT_TIMESTAMP WHERE phone = $2',
            [requiredPoints, phone]
        );

        // 记录交易
        await client.query(
            'INSERT INTO transactions (type, member_phone, member_name, points, voucher_amount) VALUES ($1, $2, $3, $4, $5)',
            ['积分兑换', phone, member.name, -requiredPoints, voucherAmount]
        );

        await client.query('COMMIT');

        res.json({
            success: true,
            message: `成功兑换 ${voucherAmount}元 代金券，扣除 ${requiredPoints} 积分`
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('兑换代金券错误:', error);
        res.status(500).json({ error: '服务器错误' });
    } finally {
        client.release();
    }
});

// 获取交易记录（支持分页和搜索）
app.get('/api/transactions', authenticateToken, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const search = req.query.search || '';
        const offset = (page - 1) * limit;

        let whereClause = '';
        let queryParams = [];
        let paramIndex = 1;

        // 如果有搜索条件
        if (search) {
            whereClause = `WHERE (member_name ILIKE $${paramIndex} OR member_phone ILIKE $${paramIndex + 1})`;
            queryParams.push(`%${search}%`, `%${search}%`);
            paramIndex += 2;
        }

        // 获取总记录数
        const countQuery = `SELECT COUNT(*) FROM transactions ${whereClause}`;
        const countResult = await pool.query(countQuery, queryParams);
        const totalRecords = parseInt(countResult.rows[0].count);
        const totalPages = Math.ceil(totalRecords / limit);

        // 获取分页数据
        const dataQuery = `
            SELECT
                id, type, member_phone, member_name, amount, points,
                voucher_amount, ratio, created_at,
                TO_CHAR(created_at AT TIME ZONE 'Asia/Shanghai', 'YYYY/MM/DD HH24:MI:SS') as formatted_time
            FROM transactions
            ${whereClause}
            ORDER BY created_at DESC
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;

        const dataParams = [...queryParams, limit, offset];
        const result = await pool.query(dataQuery, dataParams);

        res.json({
            success: true,
            transactions: result.rows,
            pagination: {
                currentPage: page,
                totalPages: totalPages,
                totalRecords: totalRecords,
                limit: limit,
                hasMore: page < totalPages
            }
        });
    } catch (error) {
        console.error('获取交易记录错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取代金券列表
app.get('/api/vouchers', authenticateToken, async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM vouchers ORDER BY amount ASC');
        res.json({ success: true, vouchers: result.rows });
    } catch (error) {
        console.error('获取代金券列表错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 添加代金券
app.post('/api/vouchers', authenticateToken, async (req, res) => {
    try {
        const { amount, points } = req.body;

        if (!amount || !points || amount <= 0 || points <= 0) {
            return res.status(400).json({ error: '请输入有效的代金券金额和积分' });
        }

        // 检查是否已存在相同金额的代金券
        const existing = await pool.query('SELECT id FROM vouchers WHERE amount = $1', [amount]);
        if (existing.rows.length > 0) {
            return res.status(400).json({ error: '该金额的代金券已存在' });
        }

        const result = await pool.query(
            'INSERT INTO vouchers (amount, points) VALUES ($1, $2) RETURNING *',
            [amount, points]
        );

        res.json({ success: true, voucher: result.rows[0] });
    } catch (error) {
        console.error('添加代金券错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新代金券
app.put('/api/vouchers/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { amount, points } = req.body;

        if (!amount || !points || amount <= 0 || points <= 0) {
            return res.status(400).json({ error: '请输入有效的金额和积分' });
        }

        // 检查是否与其他代金券金额冲突
        const existing = await pool.query('SELECT id FROM vouchers WHERE amount = $1 AND id != $2', [amount, id]);
        if (existing.rows.length > 0) {
            return res.status(400).json({ error: '该金额的代金券已存在' });
        }

        const result = await pool.query(
            'UPDATE vouchers SET amount = $1, points = $2 WHERE id = $3 RETURNING *',
            [amount, points, id]
        );

        if (result.rows.length === 0) {
            return res.status(404).json({ error: '代金券不存在' });
        }

        res.json({ success: true, voucher: result.rows[0] });
    } catch (error) {
        console.error('更新代金券错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 删除代金券
app.delete('/api/vouchers/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const result = await pool.query('DELETE FROM vouchers WHERE id = $1 RETURNING *', [id]);

        if (result.rows.length === 0) {
            return res.status(404).json({ error: '代金券不存在' });
        }

        res.json({ success: true, message: '代金券删除成功' });
    } catch (error) {
        console.error('删除代金券错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新交易记录
app.put('/api/transactions/:id', authenticateToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const { id } = req.params;
        const { amount, points, voucher_amount } = req.body;



        // 检查权限
        if (req.user.role !== 'admin') {
            return res.status(403).json({ error: '权限不足，只有系统管理员可以编辑交易记录' });
        }

        // 验证输入
        if (points === undefined || points === null || isNaN(points)) {
            return res.status(400).json({ error: '积分必须是有效数字' });
        }

        // 根据交易类型验证其他字段
        if (amount !== undefined && amount !== null && isNaN(amount)) {
            return res.status(400).json({ error: '金额必须是有效数字' });
        }

        if (voucher_amount !== undefined && voucher_amount !== null && isNaN(voucher_amount)) {
            return res.status(400).json({ error: '代金券金额必须是有效数字' });
        }

        // 获取原交易记录
        const originalResult = await client.query('SELECT * FROM transactions WHERE id = $1', [id]);
        if (originalResult.rows.length === 0) {
            return res.status(404).json({ error: '交易记录不存在' });
        }

        const original = originalResult.rows[0];
        const pointsDiff = points - original.points;



        // 更新会员积分和交易记录
        if (original.type === '积分获得') {
            // 验证必需字段
            if (amount === undefined || amount === null) {
                return res.status(400).json({ error: '积分获得记录必须包含销售金额' });
            }

            // 积分获得：直接使用积分差值
            await client.query(
                'UPDATE members SET points = points + $1 WHERE phone = $2',
                [pointsDiff, original.member_phone]
            );

            // 更新交易记录
            await client.query(
                'UPDATE transactions SET amount = $1, points = $2 WHERE id = $3',
                [amount, points, id]
            );


        } else if (original.type === '积分兑换') {
            // 验证必需字段
            if (voucher_amount === undefined || voucher_amount === null) {
                return res.status(400).json({ error: '积分兑换记录必须包含代金券金额' });
            }

            // 积分兑换：积分差值处理
            // 兑换记录中的积分是负数，pointsDiff也会是负数或正数
            // 例如：原来-2000改为-1000，pointsDiff = -1000-(-2000) = 1000
            // 应该给会员增加1000积分（因为兑换的积分减少了）
            await client.query(
                'UPDATE members SET points = points + $1 WHERE phone = $2',
                [pointsDiff, original.member_phone]
            );

            // 更新交易记录
            await client.query(
                'UPDATE transactions SET voucher_amount = $1, points = $2 WHERE id = $3',
                [voucher_amount, points, id]
            );


        } else {
            return res.status(400).json({ error: '未知的交易类型' });
        }

        await client.query('COMMIT');

        res.json({
            success: true,
            message: '交易记录更新成功'
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('更新交易记录错误:', error);
        console.error('错误详情:', error.stack);
        res.status(500).json({
            error: '服务器错误',
            details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    } finally {
        client.release();
    }
});

// 删除交易记录
app.delete('/api/transactions/:id', authenticateToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        const { id } = req.params;

        // 检查权限
        if (req.user.role !== 'admin') {
            return res.status(403).json({ error: '权限不足，只有系统管理员可以删除交易记录' });
        }

        // 获取交易记录
        const transactionResult = await client.query('SELECT * FROM transactions WHERE id = $1', [id]);
        if (transactionResult.rows.length === 0) {
            return res.status(404).json({ error: '交易记录不存在' });
        }

        const transaction = transactionResult.rows[0];

        // 恢复会员积分
        if (transaction.type === '积分获得') {
            await client.query(
                'UPDATE members SET points = points - $1 WHERE phone = $2',
                [transaction.points, transaction.member_phone]
            );
        } else {
            await client.query(
                'UPDATE members SET points = points + $1 WHERE phone = $2',
                [Math.abs(transaction.points), transaction.member_phone]
            );
        }

        // 删除交易记录
        await client.query('DELETE FROM transactions WHERE id = $1', [id]);

        await client.query('COMMIT');

        res.json({
            success: true,
            message: '交易记录删除成功'
        });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('删除交易记录错误:', error);
        res.status(500).json({ error: '服务器错误' });
    } finally {
        client.release();
    }
});

// 获取系统设置
app.get('/api/settings', authenticateToken, async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM system_settings');
        const settings = {};
        result.rows.forEach(row => {
            settings[row.setting_key] = row.setting_value;
        });
        res.json({ success: true, settings });
    } catch (error) {
        console.error('获取系统设置错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 更新系统设置
app.put('/api/settings', authenticateToken, async (req, res) => {
    try {
        const { defaultPointsRatio, vipPointsRatio } = req.body;

        if (!defaultPointsRatio || !vipPointsRatio || defaultPointsRatio <= 0 || vipPointsRatio <= 0) {
            return res.status(400).json({ error: '请输入有效的积分比例' });
        }

        await pool.query(
            'UPDATE system_settings SET setting_value = $1 WHERE setting_key = $2',
            [defaultPointsRatio.toString(), 'default_points_ratio']
        );

        await pool.query(
            'UPDATE system_settings SET setting_value = $1 WHERE setting_key = $2',
            [vipPointsRatio.toString(), 'vip_points_ratio']
        );

        res.json({ success: true, message: '积分比例设置已保存' });
    } catch (error) {
        console.error('更新系统设置错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 获取统计信息
app.get('/api/stats', authenticateToken, async (req, res) => {
    try {
        const memberCount = await pool.query('SELECT COUNT(*) FROM members');
        const totalPoints = await pool.query('SELECT SUM(points) FROM members');

        res.json({
            success: true,
            stats: {
                totalMembers: parseInt(memberCount.rows[0].count),
                totalPoints: parseInt(totalPoints.rows[0].sum || 0)
            }
        });
    } catch (error) {
        console.error('获取统计信息错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 导出数据
app.get('/api/export', authenticateToken, async (req, res) => {
    try {
        const members = await pool.query('SELECT * FROM members ORDER BY created_at DESC');
        const transactions = await pool.query('SELECT * FROM transactions ORDER BY created_at DESC');
        const vouchers = await pool.query('SELECT * FROM vouchers ORDER BY amount ASC');
        const settings = await pool.query('SELECT * FROM system_settings');

        const data = {
            members: members.rows,
            transactions: transactions.rows,
            vouchers: vouchers.rows,
            settings: settings.rows,
            exportTime: new Date().toISOString()
        };

        res.json({ success: true, data });
    } catch (error) {
        console.error('导出数据错误:', error);
        res.status(500).json({ error: '服务器错误' });
    }
});

// 清空所有数据
app.delete('/api/clear-all', authenticateToken, async (req, res) => {
    const client = await pool.connect();
    try {
        await client.query('BEGIN');

        await client.query('DELETE FROM transactions');
        await client.query('DELETE FROM members');
        await client.query('DELETE FROM vouchers');

        await client.query('COMMIT');

        res.json({ success: true, message: '所有数据已清空' });
    } catch (error) {
        await client.query('ROLLBACK');
        console.error('清空数据错误:', error);
        res.status(500).json({ error: '服务器错误' });
    } finally {
        client.release();
    }
});

// 提供静态文件
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 启动服务器
async function startServer() {
    try {
        await initDatabase();
        app.listen(PORT, () => {
            console.log(`🚀 服务器运行在 http://localhost:${PORT}`);
            console.log(`📱 前端页面: http://localhost:${PORT}`);
        });
    } catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}

startServer();
