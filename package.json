{"name": "member-points-system", "version": "1.0.0", "description": "会员积分管理系统后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "npx nodemon server.js", "prod": "NODE_ENV=production node server.js", "production": "node start-production.js", "install-deps": "npm install --production"}, "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["member", "points", "management"], "author": "欧妮家服装工作室", "license": "MIT"}