#!/usr/bin/env node

/**
 * 生产环境启动脚本
 * 用于在生产环境中启动会员积分管理系统
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动会员积分管理系统 - 生产模式');
console.log('📍 项目路径:', __dirname);
console.log('⏰ 启动时间:', new Date().toLocaleString());
console.log('');

// 启动Node.js服务器
const serverProcess = spawn('node', ['server.js'], {
    cwd: __dirname,
    stdio: 'inherit',
    env: {
        ...process.env,
        NODE_ENV: 'production'
    }
});

// 处理进程退出
serverProcess.on('close', (code) => {
    console.log(`\n📊 服务器进程退出，退出码: ${code}`);
    process.exit(code);
});

// 处理进程错误
serverProcess.on('error', (error) => {
    console.error('❌ 启动服务器时发生错误:', error);
    process.exit(1);
});

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('\n🛑 收到停止信号，正在关闭服务器...');
    serverProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号，正在关闭服务器...');
    serverProcess.kill('SIGTERM');
});
