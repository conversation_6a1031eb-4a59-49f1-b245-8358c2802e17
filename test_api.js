const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAPI() {
    try {
        // 登录获取token
        const loginResponse = await fetch('http://localhost:3000/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: '123456'
            })
        });

        const loginData = await loginResponse.json();
        
        if (!loginData.success) {
            console.log('登录失败:', loginData.error);
            return;
        }

        console.log('登录成功');
        const token = loginData.token;

        // 获取交易记录总数
        const transactionsResponse = await fetch('http://localhost:3000/api/transactions?page=1&limit=1', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const transactionsData = await transactionsResponse.json();
        
        if (transactionsData.success) {
            console.log('=== 交易记录统计 ===');
            console.log('总记录数:', transactionsData.pagination.totalRecords);
            console.log('总页数:', transactionsData.pagination.totalPages);
            console.log('每页记录数:', transactionsData.pagination.limit);
        } else {
            console.log('获取交易记录失败:', transactionsData.error);
        }

    } catch (error) {
        console.error('API测试失败:', error.message);
    }
}

testAPI();
