const { Pool } = require('pg');
require('dotenv').config();

// 数据库连接池
const pool = new Pool({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: false, // 禁用SSL
    max: 10,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 5000,
    acquireTimeoutMillis: 10000,
});

// 测试数据库连接
pool.on('connect', () => {
    console.log('✅ 数据库连接成功');
});

pool.on('error', (err) => {
    console.error('❌ 数据库连接错误:', err);
});

// 初始化数据库表
async function initDatabase() {
    const client = await pool.connect();
    try {
        // 创建用户表
        await client.query(`
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL,
                role VARCHAR(20) DEFAULT 'staff',
                last_login TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 创建会员表
        await client.query(`
            CREATE TABLE IF NOT EXISTS members (
                id SERIAL PRIMARY KEY,
                phone VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                points INTEGER DEFAULT 0,
                join_date DATE DEFAULT CURRENT_DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 创建交易记录表
        await client.query(`
            CREATE TABLE IF NOT EXISTS transactions (
                id SERIAL PRIMARY KEY,
                type VARCHAR(20) NOT NULL,
                member_phone VARCHAR(20) NOT NULL,
                member_name VARCHAR(100) NOT NULL,
                amount DECIMAL(10,2),
                points INTEGER NOT NULL,
                voucher_amount DECIMAL(10,2),
                ratio VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (member_phone) REFERENCES members(phone)
            )
        `);

        // 创建系统设置表
        await client.query(`
            CREATE TABLE IF NOT EXISTS system_settings (
                id SERIAL PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 创建代金券表
        await client.query(`
            CREATE TABLE IF NOT EXISTS vouchers (
                id SERIAL PRIMARY KEY,
                amount INTEGER NOT NULL,
                points INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        console.log('✅ 数据库表初始化完成');

        // 检查并添加缺失的列
        await migrateDatabase(client);

        // 插入默认用户（如果不存在）
        const bcrypt = require('bcrypt');
        const defaultUsers = [
            { username: 'admin', password: '123456', name: '管理员', role: 'admin' },
            { username: 'staff', password: '888888', name: '店员', role: 'staff' },
            { username: 'owner', password: 'onijia2024', name: '店主', role: 'owner' }
        ];

        for (const user of defaultUsers) {
            const existingUser = await client.query('SELECT id FROM users WHERE username = $1', [user.username]);
            if (existingUser.rows.length === 0) {
                const hashedPassword = await bcrypt.hash(user.password, 10);
                await client.query(
                    'INSERT INTO users (username, password, name, role) VALUES ($1, $2, $3, $4)',
                    [user.username, hashedPassword, user.name, user.role]
                );
                console.log(`✅ 创建默认用户: ${user.username}`);
            }
        }

        // 插入默认系统设置
        const defaultSettings = [
            { key: 'default_points_ratio', value: '1' },
            { key: 'vip_points_ratio', value: '2' }
        ];

        for (const setting of defaultSettings) {
            const existing = await client.query('SELECT id FROM system_settings WHERE setting_key = $1', [setting.key]);
            if (existing.rows.length === 0) {
                await client.query(
                    'INSERT INTO system_settings (setting_key, setting_value) VALUES ($1, $2)',
                    [setting.key, setting.value]
                );
            }
        }

        // 插入默认代金券
        const defaultVouchers = [
            { amount: 50, points: 500 },
            { amount: 100, points: 900 },
            { amount: 200, points: 1600 },
            { amount: 500, points: 3500 }
        ];

        const voucherCount = await client.query('SELECT COUNT(*) FROM vouchers');
        if (parseInt(voucherCount.rows[0].count) === 0) {
            for (const voucher of defaultVouchers) {
                await client.query(
                    'INSERT INTO vouchers (amount, points) VALUES ($1, $2)',
                    [voucher.amount, voucher.points]
                );
            }
            console.log('✅ 创建默认代金券');
        }

    } catch (err) {
        console.error('❌ 数据库初始化错误:', err);
        throw err;
    } finally {
        client.release();
    }
}

// 数据库迁移函数
async function migrateDatabase(client) {
    try {
        console.log('🔄 检查数据库结构更新...');

        // 检查users表是否有last_login列
        const lastLoginCheck = await client.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'last_login'
        `);

        if (lastLoginCheck.rows.length === 0) {
            console.log('📝 添加last_login列到users表...');
            await client.query('ALTER TABLE users ADD COLUMN last_login TIMESTAMP');
            console.log('✅ last_login列添加成功');
        }

        // 检查users表是否有updated_at列
        const updatedAtCheck = await client.query(`
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'updated_at'
        `);

        if (updatedAtCheck.rows.length === 0) {
            console.log('📝 添加updated_at列到users表...');
            await client.query('ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP');
            console.log('✅ updated_at列添加成功');
        }

        console.log('✅ 数据库结构更新完成');

    } catch (error) {
        console.error('❌ 数据库迁移错误:', error);
        // 不抛出错误，继续执行
    }
}

module.exports = {
    pool,
    initDatabase
};
