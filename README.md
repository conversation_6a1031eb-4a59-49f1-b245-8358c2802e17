# 欧妮家服装工作室会员积分管理系统

一个基于Node.js + PostgreSQL的现代化会员积分管理系统，专为小型服装工作室设计。

## 🌟 功能特性

### 👤 用户管理
- 安全的用户登录系统
- 多角色支持（管理员、店员、店主）
- JWT令牌认证
- 用户增删改查（仅管理员和店主）
- 角色权限控制

### 👥 会员管理
- 添加新会员
- 会员信息查询
- 会员积分统计

### 💰 积分系统
- 销售积分添加
- 支持不同积分比例（默认会员/VIP会员）
- 积分兑换代金券
- 完整的交易记录

### 🎫 代金券管理
- 自定义代金券金额和积分
- 代金券的增删改查
- 灵活的兑换规则

### 📊 数据管理
- 实时统计信息
- 数据导出功能
- 数据清空功能

## 🚀 快速开始

### 环境要求
- Node.js 14.0 或更高版本
- PostgreSQL 数据库

### 安装步骤

1. **下载项目文件**
   ```
   将所有项目文件放在同一个文件夹中
   ```

2. **安装依赖**
   ```bash
   双击运行 install.bat
   ```
   或手动执行：
   ```bash
   npm install
   ```

3. **配置数据库**
   - 确保PostgreSQL数据库服务正在运行
   - 数据库配置已在 `.env` 文件中设置：
     - 地址：*************
     - 端口：5432
     - 数据库名：member_db
     - 用户名：member_db
     - 密码：198497

4. **启动服务**
   ```bash
   双击运行 start.bat
   ```
   或手动执行：
   ```bash
   node server.js
   ```

5. **访问系统**
   - 打开浏览器访问：http://localhost:3000
   - 使用预设账户登录

## 🔐 默认账户

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| `admin` | `123456` | 管理员 | 系统管理员 |
| `staff` | `888888` | 店员 | 普通店员 |
| `owner` | `onijia2024` | 店主 | 店铺老板 |

## 📱 使用说明

### 登录系统
1. 在登录页面输入用户名和密码
2. 点击"登录"按钮
3. 登录成功后自动跳转到主界面

### 销售积分
1. 点击"销售积分"标签
2. 输入会员手机号
3. 输入销售金额
4. 选择积分比例（默认会员/VIP会员）
5. 点击"添加积分"

### 积分兑换
1. 点击"积分兑换"标签
2. 输入会员手机号并查询
3. 选择要兑换的代金券
4. 确认兑换

### 会员管理
1. 点击"会员管理"标签
2. 查看会员统计信息
3. 添加新会员或查看会员列表

### 交易记录
1. 点击"交易记录"标签
2. 查看所有积分获得和兑换记录

### 系统设置
1. 点击"设置"标签
2. 在设置页面中有两个选项卡：

   **系统设置选项卡**：
   - 调整积分比例
   - 管理代金券
   - 导出或清空数据

   **用户管理选项卡**（仅管理员和店主可见）：
   - 查看用户统计信息（总用户数、在线用户）
   - 添加新用户（用户名、姓名、密码、角色）
   - 编辑用户信息（姓名、角色、密码）
   - 删除用户（带确认对话框）
   - 搜索用户（按姓名、用户名、角色）
   - 查看最后登录时间
   - 当前用户保护（不能删除自己）

## 🗄️ 数据库结构

系统会自动创建以下数据表：

- `users` - 用户表
- `members` - 会员表
- `transactions` - 交易记录表
- `system_settings` - 系统设置表
- `vouchers` - 代金券表

## 🔧 技术栈

- **后端**: Node.js + Express
- **数据库**: PostgreSQL
- **前端**: HTML5 + CSS3 + JavaScript
- **认证**: JWT
- **样式**: 响应式设计，支持移动设备

## 📝 注意事项

1. **数据安全**: 系统使用PostgreSQL数据库存储，数据持久化且安全
2. **备份**: 建议定期使用"导出数据"功能备份数据
3. **密码**: 建议在生产环境中修改默认密码
4. **网络**: 确保服务器能够访问PostgreSQL数据库

## 🆘 常见问题

### Q: 无法连接数据库？
A: 请检查：
- PostgreSQL服务是否运行
- 网络连接是否正常
- 数据库配置信息是否正确

### Q: 登录失败？
A: 请确认：
- 用户名和密码是否正确
- 数据库连接是否正常
- 服务器是否正常运行

### Q: 页面无法访问？
A: 请检查：
- 服务器是否启动成功
- 端口3000是否被占用
- 防火墙设置

## 📞 技术支持

如有问题，请检查：
1. 控制台错误信息
2. 数据库连接状态
3. 网络连接情况

## 📄 许可证

MIT License - 欧妮家服装工作室专用版本
